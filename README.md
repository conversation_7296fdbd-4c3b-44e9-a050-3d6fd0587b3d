[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=dbdventures_core-microservices-nx&metric=alert_status&token=3a34ed7a2f434c37461a6954b123017352ac01a7)](https://sonarcloud.io/summary/new_code?id=dbdventures_core-microservices-nx)

# Core Microservices NX

## Setup

Install Bun <https://bun.sh/docs/installation>

clear old node modules if needed

```sh
bun clear-node-modules
bun i
```

(Optional) if using e2e you may need this


```sh
bunx playwright install
```

For LLM support also install:

```sh
brew install pipx
pipx ensurepath
pipx install poetry
```

### Preflight Setup 🫡

#### General

- [ ] Ensure you have the required env settings for all applications. A developer can send via Doppler

#### Residual Service

```sh
cp apps/residuals-service/.env.test apps/residuals-service/.env.local
```

#### Partner Portal (UI)

```sh
cp apps/ui-partner-portal/.example.env.local apps/ui-partner-portal/.env.local

## Build

```sh
bunx nx run-many --target=build
```

## Test

```sh
bunx nx run-many -t test --exclude core-db
```

## Run (example)

```sh
bunx nx serve ui-partner-portal
```

## Remote caching

Run `bunx nx connect-to-nx-cloud` to enable [remote caching](https://nx.app) and make CI faster.

## Further help

Run `bunx nx graph` to see a diagram of the dependencies of the projects.

Visit the [Nx Documentation](https://nx.dev) to learn more.

## Working with Nx

- View the web version of a project `bun nx show project <project> --web`
- View the graph of a project `bun nx graph <project>`
- View the graph of all projects `bun nx graph`
- Run a command in a project `bun nx run <project> <command>`
- Run a command in all projects `bun nx run <command>`

## Package Management

PRs that mutate the `package.json` and `bun-lock.yaml` will trigger an audit of the added depency using [Syncpack](https://jamiemason.github.io/syncpack/).
Syncpack enable managing external and internal dependencies to our desired specifications.

To run the commands -> `bun pkg:audit` && `bun pkg:audit-fix`.

## Additional Commands

See also [Nx Commands](https://nx.dev/packages/nx/documents)

```sh
bunx nx build <target>
bunx nx test <target>
bunx nx format
bunx nx format:check
bunx nx serve express-sample
bunx nx serve sample-app --configuration=production
bunx nx run sample-app:build --configuration=production
```

## Adding new Libraries

```shell
bunx nx g @nx/js:lib new-lib
```

## Upload Containers

```sh
aws-vault exec shared-services-account -- aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
doppler run -p forward-apps -c prd -- bun nx run-many --target=container --configuration=production
# or
doppler run -p forward-apps -c prd -- bun nx run notifications-service:container:production
```

## Conventional Commit

The format for commit messages and PRs should be
type(app|lib): JIRA#(option) Message

### Feature commit

```
feat(api-key-service): DBD-001 Update token endpoint
```

#### A commit with no special meaning

```
chore: Fix XAML parsing sample
```

Use this command to generate a commit with proper message.

```sh
bun cz
```

### Fixing up commits

If you already made commits, and they don't meet the Conventional Commits specification, you have a couple of options:
if there's only one commit to redo, the easiest option is to use git commit --amend with no staged changes,
which will allow you to edit the commit message.
if you have multiple commits to reformat, you'll probably need to do an interactive rebase and use the reword option.

Another good article if you get stuck:
[Fix Commit Messages](https://docs.github.com/en/pull-requests/committing-changes-to-your-project/creating-and-editing-commits/changing-a-commit-message)

## Pull Request (PR) Conventions and Best Practices

### PR Naming

PRs follow conventional commits, but require a JIRA Ticket.

### Code Reviews

When creating and reviewing Pull Requests (PRs), it's important to adhere to certain conventions and best practices to ensure the smooth functioning of the development process. Here are some guidelines to follow:

- **Descriptive Titles:** Provide a clear and concise title that summarizes the changes introduced by the PR.
- **Detailed Descriptions:** Include a detailed description of the changes made, explaining the problem solved and the approach taken.
- **Small, Atomic Commits:** Break down changes into small, logical commits with clear messages. This helps in understanding the evolution of the codebase and makes it easier to review.
- **Review Requests:** Request reviews from appropriate team members, ensuring that changes are thoroughly examined before merging.
- **Address Feedback:** Respond to feedback promptly and make necessary changes to the code as suggested by reviewers.
- **Testing:** Ensure that the code is adequately tested, and any new features or fixes come with corresponding test cases.
- **Documentation:** Update relevant documentation to reflect the changes made in the PR.
- **Follow Coding Standards:** Maintain consistency with coding style and adhere to established coding standards of the project.

For detailed guidance on code reviewing best practices, refer to Google Engineering's resources for both code reviewers and change authors:

Google's Code Review Guidelines

- [The Code Reviewer's Guide](https://google.github.io/eng-practices/review/reviewer/)
- [The Change Author's Guide](https://google.github.io/eng-practices/review/developer/)

By following these practices, we can streamline our development workflow and ensure the quality and maintainability of our codebase.

### Conventional comments

To add conventional comments labels to GitHub as saved replies, follow these steps:

1. Go to [GitHub's saved replies settings](https://github.com/settings/replies).
2. Open Developer Tools.
3. Copy and paste the provided JavaScript code into the JavaScript console.
4. Press enter.

```js
{
  const LABELS = [
    ["👏 praise", "Praises highlight something positive. Try to leave at least one of these comments per review (if it exists :^)"],
    ["🤓 nitpick", "Nitpicks are small, trivial, but necessary changes. Distinguishing nitpick comments significantly helps direct the reader's attention to comments requiring more involvement."],
    ["🎯 suggestion", "Suggestions are specific requests to improve the subject under review. It is assumed that we all want to do what's best, so these comments are never dismissed as “mere suggestions”, but are taken seriously."],
    ["🔨 issue", "Issues represent user-facing problems. If possible, it's great to follow this kind of comment with a suggestion."],
    ["❔ question", "Questions are appropriate if you have a potential concern but are not quite sure if it's relevant or not. Asking the author for clarification or investigation can lead to a quick resolution."],
    ["💭 thought", "Thoughts represent an idea that popped up from reviewing. These comments are non-blocking by nature, but they are extremely valuable and can lead to more focused initiatives and mentoring opportunities."],
    ["💣 chore", "Chores are simple tasks that must be done before the subject can be “officially” accepted. Usually, these comments reference some common process. Try to leave a link to the process description so that the reader knows how to resolve the chore."],
  ];
  const form = document.querySelector(".new_saved_reply");
  const authenticity_token = encodeURIComponent(
    form.querySelector("[name=authenticity_token]").value
  );
  Promise.all(
    LABELS.map(([type, note], index) => {
      const title = encodeURIComponent(`${type[0].toUpperCase()}${type.slice(1)}`);
      const body = encodeURIComponent(`<!-- ${note}  -->\n**${type}:** ‏`);
      return fetch("replies", {
        headers: {
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
          "accept-language": "en-US,en;q=0.9",
          "cache-control": "no-cache",
          "content-type": "application/x-www-form-urlencoded",
          pragma: "no-cache",
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
        },
        referrer: "https://github.com/settings/replies",
        referrerPolicy: "strict-origin-when-cross-origin",
        body: `authenticity_token=${authenticity_token}&title=${title}&saved_reply_id=&body=${body}&path=&line=&start_line=&preview_side=&preview_start_side=&start_commit_oid=&end_commit_oid=&base_commit_oid=&comment_id=`,
        method: "POST",
        mode: "cors",
        credentials: "include",
      })
    })
  ).then(() => console.log("All added! Refresh the page!"));
}
```

Inspired by this GitHub gist: https://gist.github.com/pauloportella/7bfc0afbb46c7373098013e21f1e5614


### Adding a New Service

- Create a new service in the `apps` folder
- Create a Dockerfile is needed
- Create an ECR repository - make sure to set the permissions to allow cluster to access the repository
- Create branch config in doppler for your service
- Create and IRSA configuration in platform for the service
- create doppler token secret in k8s see gitop helm "Adding Secrets" you first add the token to k8s, then a doppler link to the config is created from the service chart
- add the service to gitops helm
- update the scripts in gitops helm to update the image tag


### Installation Issues
in package.json we had to use overrides to fix mismatches with AWS libs as well as
```
    "mimic-fn": "^2.1.0",
    "wrap-ansi": "^6.2.0",
    "ansi-styles": "^4.3.0",
    "string-width": "^4.2.3",
    "strip-ansi": "^6.0.1"
```
which fixes an old dependency in cypress that needs cjs modules to perform postinstall
without these you get
```
Error [ERR_REQUIRE_ESM]: require() of ES Module /Users/<USER>/NodeProjects/Payco/core-microservices-nx/node_modules/wrap-ansi/index.js from /Users/<USER>/NodeProjects/Payco/core-microservices-
```
errors
