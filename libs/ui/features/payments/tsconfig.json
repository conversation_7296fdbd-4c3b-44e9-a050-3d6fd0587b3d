{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../core-types"}, {"path": "../../../flagsmith"}, {"path": "../forms"}, {"path": "../../../zod-types"}, {"path": "../data-table"}, {"path": "../../../tailwind-components"}, {"path": "../../../reporting/common/server"}, {"path": "../../../next-sessions"}, {"path": "../../../zod-types-common"}, {"path": "../../../reporting/payments"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}