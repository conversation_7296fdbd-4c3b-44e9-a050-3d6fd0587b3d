'use client';

import type { AuthUser } from '@dbd/reporting-auth/auth.types';
import type { BusinessRow } from '@dbd/reporting-businesses/business.types';
import { MerchantApplicationRow } from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import { Button } from '@dbd/ui/components/button';
import { DialogTrigger } from '@dbd/ui/components/dialog';
import { Form, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@dbd/ui/components/form';
import { Link } from '@dbd/ui/components/next-link';
import { usePrevious } from '@dbd/ui/hooks/use-previous';
import { hasGroups } from '@dbd/ui/lib/user';
import { BusinessFormDialog } from '@dbd/ui-businesses/blocks/business-form';
import { CreateBusinessSubmitHandler } from '@dbd/ui-businesses/blocks/business-form.actions';
import { BusinessSelectFormInput } from '@dbd/ui-businesses/components/business-select-form-input';
import { useBusinessesList } from '@dbd/ui-businesses/hooks/use-business-list';
import { AddressFormBlock } from '@dbd/ui-forms/blocks/address-form-block';
import { DatePickerInput } from '@dbd/ui-forms/components/date-picker-form-input';
import { EINInput } from '@dbd/ui-forms/components/ein-form-input';
import { FormSection } from '@dbd/ui-forms/components/form-section';
import { NumberInput } from '@dbd/ui-forms/components/number-form-input';
import { PercentInput } from '@dbd/ui-forms/components/percent-form-input';
import { SelectInput } from '@dbd/ui-forms/components/select-form-input';
import { SSNInput } from '@dbd/ui-forms/components/ssn-form-input';
import { TextInput } from '@dbd/ui-forms/components/text-form-input';
import { useHookForm, UseHookFormProps } from '@dbd/ui-forms/hooks/use-hook-form';
import { flattenErrors } from '@dbd/ui-forms/lib/utils';
import { ProcessingPlanDetails } from '@dbd/ui-partners/blocks/processing-plan';
import { MccCodeSelectFormInput } from '@dbd/ui-partners/components/mcc-code-select-form-input';
import { PartnerSelectFormInput } from '@dbd/ui-partners/components/partner-select-form-input';
import { ProcessingPlanSelectFormInput } from '@dbd/ui-partners/components/processing-plan-select-form-input';
import { tsr as partnerTsr } from '@dbd/ui-partners/lib/partner.query-client';
import { mapProcessingPlanRowToFormSchema } from '@dbd/ui-partners/lib/utils';
import { ProcessingPlanFormSchema } from '@dbd/ui-partners/schemas/processing-plan-form-schema';
import { SalesAgentSelectFormInput } from '@dbd/ui-residuals/components/sales-agent-select-form-input';
import { SalesCodeSelectFormInput } from '@dbd/ui-residuals/components/sales-code-select-form-input';
import type { MerchantAccountOwnershipType } from '@dbd/zod-types';
import {
  type BusinessId,
  PartnerId,
  type PartnerProcessingPlanId,
  type SalesCodeId,
} from '@dbd/zod-types-common/constants';
import { zodResolver } from '@hookform/resolvers/zod';
import { subDays } from 'date-fns';
import { Plus, TriangleAlert } from 'lucide-react';
import React, { useCallback, useEffect } from 'react';
import {
  type FieldError,
  type FormState,
  useFieldArray,
  UseFormHandleSubmit,
  type UseFormReturn,
} from 'react-hook-form';

import { OwnerCard } from '../components/owner-card.js';
import { MerchantApplicationFormSchema } from '../schemas/merchant-application-form-schema.js';
import { MerchantOwnerFormSchema, OwnerTypeByOwnershipType } from '../schemas/merchant-owner-form-schema.js';
import { MerchantOwnerDialog } from './merchant-owner-form.js';

const OwnersTitleByOwnershipType = {
  SOLO_TRADER: 'Business Owners',
  PARTNERSHIP: 'Business Owners',
  PUBLIC: 'Administrators',
  PRIVATE: 'Business Owners',
  TAX_EXEMPT_ORGANIZATION: 'Administrators',
  LIMITED_LIABILITY_COMPANY: 'Business Owners',
  GOVERNMENT: 'Administrators',
} as const;

const OwnersDescByOwnershipType = {
  SOLO_TRADER: 'Please add the owner with 100% ownership.',
  PARTNERSHIP: 'Please add a primary representative and any owners with 25% or more ownership.',
  PUBLIC: 'Please add a primary representative and all administrators.',
  PRIVATE: 'Please add a primary representative and any owners with 25% or more ownership.',
  TAX_EXEMPT_ORGANIZATION: 'Please add a primary representative and all administrators.',
  LIMITED_LIABILITY_COMPANY: 'Please add a primary representative and any owners with 25% or more ownership.',
  GOVERNMENT: 'Please add a primary representative and all administrators.',
} as const;

export const OwnershipTypeOptions = [
  { label: 'Government', value: 'GOVERNMENT' },
  { label: 'Sole Proprietorship / Individual', value: 'SOLO_TRADER' },
  { label: 'Partnership', value: 'PARTNERSHIP' },
  { label: 'Public', value: 'PUBLIC' },
  { label: 'Private', value: 'PRIVATE' },
  { label: 'Tax Exempt Organization', value: 'TAX_EXEMPT_ORGANIZATION' },
  { label: 'Limited Liability Company', value: 'LIMITED_LIABILITY_COMPANY' },
];

export const MerchantAccountCategoryOptions = [
  { label: 'Retail', value: 'RETAIL' },
  { label: 'Restaurant', value: 'RESTAURANT' },
  { label: 'Motor Vehicles', value: 'MOTO' },
  { label: 'E-Commerce', value: 'ECOMMERCE' },
  { label: 'Lodging', value: 'LODGING' },
  { label: 'Car Rental', value: 'CAR_RENTAL' },
  { label: 'Petroleum', value: 'PETROLEUM' },
  { label: 'QSR', value: 'QSR' },
];

export const getErrorLabel = (path: string[]) => {
  const key = path.join('.');
  switch (key) {
    case 'partnerId':
      return 'Partner';
    case 'businessId':
      return 'Business';
    case 'plan_id':
      return 'Plan Name';
    case 'name':
      return 'Account Name';
    case 'company.legal_name':
      return 'Legal Name';
    case 'company.dba_name':
      return 'DBA Name';
    case 'company.description':
      return 'Description';
    case 'company.business_start_date':
      return 'Start or Incorporation Date';
    case 'company.ownership_type':
      return 'Ownership Type';
    case 'company.tax_id':
      return 'Tax ID';
    case 'company.category':
      return 'Category';
    case 'company.mcc_code':
      return 'MCC Code';
    case 'company.address':
      return 'Address';
    case 'company.phone_number':
      return 'Phone Number';
    case 'company.website':
      return 'Website';
    case 'company.stock_symbol':
      return 'Stock Symbol';
    case 'address.address_line1':
      return 'Address Line 1';
    case 'address.address_line2':
      return 'Address Line 2';
    case 'address.city':
      return 'City';
    case 'address.state':
      return 'State/Province';
    case 'address.zip_code':
      return 'Postal Code';
    case 'address.country':
      return 'Country';
    case 'owners':
      return 'Administrators';
    case 'sales_code_id':
      return 'Sales Code';
    case 'sales_agent_user_id':
      return 'Sales Agent';
    case 'industry_volume.avg_annual_volume':
      return 'Average Annual Volume';
    case 'industry_volume.avg_ticket_amount':
      return 'Average Card Amount';
    case 'industry_volume.high_ticket_amount':
      return 'High Card Amount';
    case 'service_deliveries.same_day_delivery':
      return 'Same Day Delivery';
    case 'service_deliveries.one_week_delivery':
      return 'One Week Delivery';
    case 'service_deliveries.two_week_delivery':
      return 'Two Week Delivery';
    case 'service_deliveries.one_month_delivery':
      return 'One Month Delivery';
    case 'service_deliveries.more_than_one_month_delivery':
      return 'More Than One Month Delivery';
    case 'transaction_modes.in_person':
      return 'In Person';
    case 'transaction_modes.online':
      return 'Online';
    default:
      return key;
  }
};

export const MerchantApplicationFormSidebar = ({ showAgreement }: { showAgreement?: boolean }) => {
  return (
    <div className="sticky left-0 top-0 -mt-8 hidden h-fit w-[200px] min-w-[200px] space-y-4 pt-8 xl:block">
      <div className="space-y-2">
        <div className="text-xs font-bold text-gray-600 dark:text-foreground">Merchant Information</div>
        <div className="space-y-2 pl-4">
          <div>
            <Link className="text-xs text-primary hover:underline" href="#merchant-details" prefetch={false} replace>
              Merchant Details
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#contact-information" prefetch={false} replace>
              Contact Information
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#owners" prefetch={false} replace>
              Owners
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#sales-details" prefetch={false} replace>
              Sales Details
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#partner-processing" prefetch={false} replace>
              Partner Processing
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#payment-volume" prefetch={false} replace>
              Payment Volume
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#payment-amounts" prefetch={false} replace>
              Payment Amounts
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#payment-mix" prefetch={false} replace>
              Payment Mix
            </Link>
          </div>
          <div>
            <Link className="text-xs text-primary hover:underline" href="#delivery-time" prefetch={false} replace>
              Delivery Time
            </Link>
          </div>
        </div>
      </div>

      {/* {hasErrors && (
        <div className="w-fit space-y-2 rounded-md bg-red-100 p-4 dark:bg-red-900">
          <div className="flex text-xs font-bold text-red-600 dark:text-red-400">Errors</div>
          <div className="space-y-2 pl-4">
            {errors.map((error, index) => (
              <div key={error.path.join('.') + index}>
                <div
                  onClick={() => {
                    error.ref?.focus?.();
                  }}
                  className="font-regular cursor-pointer select-none text-xs text-red-500 underline">{getErrorLabel(error.path)}</div>
              </div>
            ))}
          </div>
        </div>
      )} */}

      {showAgreement && (
        <div className="space-y-2">
          <div className="text-xs font-bold text-gray-600 dark:text-foreground">Sub Merchant Agreement</div>
          <div className="space-y-2 pl-4">
            <div>
              <Link className="text-xs text-primary hover:underline" href="#final-agreement" prefetch={false} replace>
                Final Agreement
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const MerchantDetails = ({
  form,
  user,
}: {
  form: UseFormReturn<MerchantApplicationFormSchema>;
  user: AuthUser;
}) => {
  const partnerId = form.watch('partnerId');
  const ownershipType = form.watch('company.ownership_type') as MerchantAccountOwnershipType;
  const { refetch: refetchBusinesses } = useBusinessesList(
    'listBusinessNames',
    { partnerId: partnerId as PartnerId, search: '' },
    !!partnerId,
  );
  const defaultPartnerId = form.formState.defaultValues?.partnerId;
  const defaultOwnershipType = form.formState.defaultValues?.company?.ownership_type;

  useEffect(() => {
    if (ownershipType && ownershipType !== defaultOwnershipType) {
      form.setValue('company.tax_id', { mask: '', value: '' });
      form.setValue('owners', []);
    }
  }, [ownershipType, defaultOwnershipType, form]);

  useEffect(() => {
    if (partnerId && partnerId !== defaultPartnerId) {
      form.setValue('businessId', undefined as unknown as BusinessId);
    }
  }, [partnerId, defaultPartnerId, form]);

  const onBusinessChange = useCallback(
    async (business: BusinessRow) => {
      await refetchBusinesses();
      form.setValue('businessId', business.business_id);
    },
    [form, refetchBusinesses],
  );

  return (
    <FormSection id="merchant-details" title="Merchant Details">
      {hasGroups(user, ['TENANT_ACCOUNTING', 'TENANT_UNDERWRITER', 'TENANT_ADMIN', 'TENANT_ANALYST']) && (
        <FormField
          control={form.control}
          name="partnerId"
          render={({ field }) => (
            <PartnerSelectFormInput field={field} label="Partner" data-testid="merchant-details-partner-select" />
          )}
        />
      )}

      <div className="flex flex-col items-center">
        <FormField
          control={form.control}
          name="businessId"
          render={({ field }) => (
            <BusinessSelectFormInput
              field={field}
              label="Business"
              description="You can select an existing business or create a new one."
              partnerId={partnerId as PartnerId}
              data-testid="merchant-details-business-select"
            />
          )}
        />
        <BusinessFormDialog
          partnerId={partnerId as PartnerId}
          onSuccess={onBusinessChange}
          onSubmit={CreateBusinessSubmitHandler}
        >
          <div className="flex w-full items-center justify-end">
            <DialogTrigger asChild disabled={!partnerId}>
              <Button size="sm" variant="link" disabled={!partnerId} data-testid="merchant-details-add-business-button">
                <Plus className="size-4" />
                Add Business
              </Button>
            </DialogTrigger>
          </div>
        </BusinessFormDialog>
      </div>

      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <TextInput field={field} label="Account Name" required data-testid="merchant-details-account-name-input" />
        )}
      />

      <FormField
        control={form.control}
        name="company.legal_name"
        render={({ field }) => (
          <TextInput field={field} label="Legal Name" data-testid="merchant-details-legal-name-input" />
        )}
      />

      <FormField
        control={form.control}
        name="company.dba_name"
        render={({ field }) => (
          <TextInput field={field} label="DBA Name" data-testid="merchant-details-dba-name-input" />
        )}
      />

      <FormField
        control={form.control}
        name="company.description"
        render={({ field }) => (
          <TextInput field={field} label="Description" data-testid="merchant-details-description-input" />
        )}
      />

      <FormField
        control={form.control}
        name="company.business_start_date"
        render={({ field }) => (
          <DatePickerInput
            field={field}
            label="Start or Incorporation Date"
            toDate={subDays(new Date(), 1)}
            data-testid="merchant-details-start-date-input"
          />
        )}
      />

      <FormField
        control={form.control}
        name="company.ownership_type"
        render={({ field }) => (
          <SelectInput
            field={field}
            label="Ownership Type"
            options={OwnershipTypeOptions}
            data-testid="merchant-details-ownership-type-select"
          />
        )}
      />

      {ownershipType && ownershipType === 'PUBLIC' && (
        <FormField
          control={form.control}
          name="company.stock_symbol"
          render={({ field }) => (
            <TextInput field={field} label="Stock Symbol" data-testid="merchant-details-stock-symbol-input" />
          )}
        />
      )}

      {(!ownershipType || ownershipType === 'SOLO_TRADER') && (
        <FormField
          control={form.control}
          name="company.tax_id"
          render={({ field }) => (
            <SSNInput
              field={field}
              label={`Tax ID` + (ownershipType === 'SOLO_TRADER' ? ' (SSN)' : '')}
              disabled={!ownershipType}
              data-testid="merchant-details-ssn-input"
            />
          )}
        />
      )}
      {ownershipType && ownershipType !== 'SOLO_TRADER' && (
        <FormField
          control={form.control}
          name="company.tax_id"
          render={({ field }) => (
            <EINInput field={field} label="Tax ID (EIN)" data-testid="merchant-details-ein-input" />
          )}
        />
      )}

      <div className="flex items-center gap-4">
        <FormField
          control={form.control}
          name="company.category"
          render={({ field }) => (
            <SelectInput
              field={field}
              label="Business Category"
              options={MerchantAccountCategoryOptions}
              className="w-1/2"
              data-testid="merchant-details-category-select"
            />
          )}
        />

        <FormField
          control={form.control}
          name="company.mcc"
          render={({ field }) => (
            <MccCodeSelectFormInput
              field={field}
              label="Business MCC"
              className="w-1/2"
              partnerId={partnerId as PartnerId}
              data-testid="merchant-details-mcc-select"
            />
          )}
        />
      </div>
    </FormSection>
  );
};

export const ContactInformation = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  return (
    <FormSection id="contact-info" title="Contact Information">
      <FormField
        control={form.control}
        name="company.phone_number"
        render={({ field }) => (
          <TextInput
            field={field}
            label="Business Phone"
            mask={{
              mask: ['(', /\d/, /\d/, /\d/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/],
            }}
            data-testid="contact-info-phone-input"
          />
        )}
      />

      <FormField
        control={form.control}
        name="company.website"
        render={({ field }) => <TextInput field={field} label="Website" data-testid="contact-info-website-input" />}
      />

      <AddressFormBlock form={form} path="address" data-testid-prefix="contact-info" />
    </FormSection>
  );
};

export const Owners = ({ form, canEdit }: { form: UseFormReturn<MerchantApplicationFormSchema>; canEdit: boolean }) => {
  const ownershipType = form.watch('company.ownership_type') as MerchantAccountOwnershipType | undefined;

  const fieldArray = useFieldArray({
    control: form.control,
    name: 'owners',
  });

  const append = useCallback(
    async (data: MerchantOwnerFormSchema) => {
      fieldArray.append(data);
    },
    [fieldArray],
  );

  const onEdit = useCallback(
    async (index: number, data: MerchantOwnerFormSchema) => {
      await fieldArray.update(index, data);
    },
    [fieldArray],
  );

  if (!ownershipType) {
    return (
      <FormSection id="owners" title="Owners">
        <div className="border-foreground-200 flex w-full items-center justify-center rounded-md border border-dashed p-2">
          <span>Select an ownership type to add owners</span>
        </div>
      </FormSection>
    );
  }

  const type = OwnerTypeByOwnershipType[ownershipType];
  const title = OwnersTitleByOwnershipType[ownershipType];
  const desc = OwnersDescByOwnershipType[ownershipType];

  return (
    <FormSection id="owners" title={title}>
      <FormField
        control={form.control}
        name="owners"
        render={() => (
          <FormItem>
            <FormDescription>{desc}</FormDescription>
            <div className="flex w-full flex-col items-center gap-2">
              {fieldArray.fields.map((owner, index) => (
                <FormField
                  key={`owner-${index}`}
                  control={form.control}
                  name={`owners.${index}`}
                  render={() => (
                    <FormItem>
                      <OwnerCard
                        className="w-full"
                        key={`owner-${index}`}
                        owner={owner}
                        ownershipType={ownershipType}
                        errors={form.formState.errors.owners?.[index]}
                        data-testid={`owners-card-${index}`}
                        {...(canEdit ? { onEdit: (data) => onEdit(index, data) } : {})}
                        {...(canEdit ? { onDelete: () => fieldArray.remove(index) } : {})}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
              {fieldArray.fields.length === 0 && (
                <div className="border-foreground-200 flex w-full items-center justify-center rounded-md border border-dashed p-2">
                  <MerchantOwnerDialog onSuccess={append} ownershipType={ownershipType} asChild>
                    <Button size="sm" variant="link" data-testid="owners-add-first-button">
                      <Plus className="size-4" />
                      Add {type}
                    </Button>
                  </MerchantOwnerDialog>
                </div>
              )}
              {ownershipType !== 'SOLO_TRADER' && fieldArray.fields.length > 0 && (
                <MerchantOwnerDialog onSuccess={append} ownershipType={ownershipType} asChild>
                  <div className="flex w-full items-center justify-end">
                    <Button size="sm" variant="link" data-testid="owners-add-another-button">
                      <Plus className="size-4" />
                      Add {type}
                    </Button>
                  </div>
                </MerchantOwnerDialog>
              )}
            </div>
            <FormMessage path="root" />
          </FormItem>
        )}
      />
    </FormSection>
  );
};

export const SalesDetails = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  const salesCode = form.watch('sales_code_id') as SalesCodeId;
  const partnerId = form.watch('partnerId') as PartnerId;

  useEffect(() => {
    if (partnerId) {
      form.resetField('sales_code_id');
      form.resetField('sales_agent_id');
    }
  }, [partnerId, form]);

  useEffect(() => {
    if (salesCode) {
      form.resetField('sales_agent_id');
    }
  }, [salesCode, form]);

  return (
    <FormSection id="sales-details" title="Sales Details">
      <FormField
        control={form.control}
        name="sales_code_id"
        render={({ field }) => (
          <SalesCodeSelectFormInput
            field={field}
            label="Sales Code"
            partnerId={partnerId}
            data-testid="sales-details-code-select"
          />
        )}
      />

      <FormField
        control={form.control}
        name="sales_agent_id"
        render={({ field }) => (
          <SalesAgentSelectFormInput
            field={field}
            label="Sales Agent"
            partnerId={partnerId}
            salesCode={salesCode}
            data-testid="sales-details-agent-select"
          />
        )}
      />
    </FormSection>
  );
};

export const PartnerProcessing = ({
  form,
  canEdit,
}: {
  form: UseFormReturn<MerchantApplicationFormSchema>;
  canEdit: boolean;
}) => {
  const partnerId = form.watch('partnerId') as PartnerId;
  const plan = form.watch('plan');
  const planId = form.watch('plan_id') as PartnerProcessingPlanId;
  const defaultPartnerId = form.formState.defaultValues?.partnerId;
  const defaultPlanId = form.formState.defaultValues?.plan_id;
  const prevPlanId = usePrevious(planId, defaultPlanId);

  useEffect(() => {
    if (partnerId && partnerId !== defaultPartnerId) {
      form.setValue('plan_id', undefined as unknown as PartnerProcessingPlanId);
    }
  }, [partnerId, defaultPartnerId, form]);

  useEffect(() => {
    if (planId !== prevPlanId && planId) {
      partnerTsr.getPartnerProcessingPlanById
        .query({
          params: { id: planId },
        })
        .then((response) => {
          if (response && response.status === 200) {
            const data = ProcessingPlanFormSchema.parse(mapProcessingPlanRowToFormSchema(response.body));
            form.setValue('plan', data, { shouldDirty: false });
          }
        });
    } else if (!planId) {
      form.setValue('plan', undefined);
    }
  }, [planId, prevPlanId, form]);

  const onSubmit = useCallback(
    async (data: ProcessingPlanFormSchema) => {
      form.setValue('plan', data, { shouldDirty: true });
      return { success: true as const, data };
    },
    [form],
  );

  return (
    <FormSection id="partner-processing" title="Partner Processing Plan" data-testid="partner-processing-section">
      <div className="flex items-center gap-4">
        <FormField
          control={form.control}
          name="plan_id"
          render={({ field }) => (
            <ProcessingPlanSelectFormInput
              field={field}
              label="Plan Name"
              partnerId={partnerId}
              disabled={!canEdit}
              data-testid="partner-processing-plan-select"
            />
          )}
        />

        {plan && (
          <FormItem className="h-full w-1/4 space-y-2" data-testid="partner-processing-plan-type">
            <FormLabel>Type</FormLabel>
            <div>{plan.type}</div>
          </FormItem>
        )}
      </div>

      {plan && (
        <FormItem className="size-full space-y-2" data-testid="partner-processing-plan-description">
          <FormLabel>Description</FormLabel>
          <div>{plan.description}</div>
          <FormMessage />
        </FormItem>
      )}

      {plan && (
        <FormItem className="size-full space-y-2" data-testid="partner-processing-plan-comment">
          <FormLabel>Comment</FormLabel>
          <div>{plan.comment}</div>
          <FormMessage />
        </FormItem>
      )}

      {plan && <ProcessingPlanDetails plan={plan} onSubmit={canEdit ? onSubmit : undefined} />}
    </FormSection>
  );
};

export const PaymentVolume = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  const achEnabled = form.watch('plan.ach_enabled');
  return (
    <FormSection id="payment-volume" title="Payment Volume">
      {!achEnabled && (
        <FormField
          control={form.control}
          name="industry_volume.avg_annual_volume"
          render={({ field }) => (
            <NumberInput
              field={field}
              label="Total Annual Payment Volume"
              allowDecimals={false}
              data-testid="payment-volume-total-annual-volume-input"
            />
          )}
        />
      )}
      {achEnabled && (
        <>
          <FormLabel className="font-bold">Total Annual Payment Volume</FormLabel>
          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="industry_volume.avg_annual_volume"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="Card Payments"
                  allowDecimals={false}
                  data-testid="payment-volume-card-payments-input"
                />
              )}
            />
            <FormField
              control={form.control}
              name="industry_volume.avg_ach_annual_volume"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="ACH Payments"
                  allowDecimals={false}
                  data-testid="payment-volume-ach-payments-input"
                />
              )}
            />
          </div>
        </>
      )}
    </FormSection>
  );
};

export const PaymentAmounts = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  const achEnabled = form.watch('plan.ach_enabled');
  return (
    <FormSection id="payment-amounts" title="Payment Amounts">
      {!achEnabled && (
        <>
          <FormField
            control={form.control}
            name="industry_volume.avg_ticket_amount"
            render={({ field }) => (
              <NumberInput
                field={field}
                label="Average Payment Amount"
                allowDecimals={false}
                data-testid="payment-amounts-avg-card-input"
              />
            )}
          />
          <FormField
            control={form.control}
            name="industry_volume.high_ticket_amount"
            render={({ field }) => (
              <NumberInput
                field={field}
                label="High Payment Amount"
                allowDecimals={false}
                data-testid="payment-amounts-high-card-input"
              />
            )}
          />
        </>
      )}
      {achEnabled && (
        <>
          <FormLabel className="font-bold">Average Payment Amount</FormLabel>
          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="industry_volume.avg_ticket_amount"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="Card Payments"
                  allowDecimals={false}
                  data-testid="payment-amounts-avg-card-input"
                />
              )}
            />
            <FormField
              control={form.control}
              name="industry_volume.avg_ach_ticket_amount"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="ACH Payments"
                  allowDecimals={false}
                  data-testid="payment-amounts-avg-ach-input"
                />
              )}
            />
          </div>

          <FormLabel className="font-bold">High Payment Amount</FormLabel>
          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="industry_volume.high_ticket_amount"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="Card Payments"
                  allowDecimals={false}
                  data-testid="payment-amounts-high-card-input"
                />
              )}
            />
            <FormField
              control={form.control}
              name="industry_volume.ach_high_ticket_amount"
              render={({ field }) => (
                <NumberInput
                  field={field}
                  label="ACH Payments"
                  allowDecimals={false}
                  data-testid="payment-amounts-high-ach-input"
                />
              )}
            />
          </div>
        </>
      )}
    </FormSection>
  );
};

export const PaymentMix = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  const total = form.watch('transaction_modes.in_person') + form.watch('transaction_modes.online');

  const isDirty =
    form.formState.dirtyFields.transaction_modes?.in_person || form.formState.dirtyFields.transaction_modes?.online;

  return (
    <FormSection id="payment-mix" title="Payment Mix">
      <div className="flex items-center gap-4">
        <FormField
          control={form.control}
          name="transaction_modes.in_person"
          render={({ field }) => (
            <PercentInput field={field} label="In Person" className="w-1/2" data-testid="payment-mix-in-person-input" />
          )}
        />

        <FormField
          control={form.control}
          name="transaction_modes.online"
          render={({ field }) => (
            <PercentInput field={field} label="Online" className="w-1/2" data-testid="payment-mix-online-input" />
          )}
        />
      </div>

      <div className="flex w-full flex-wrap items-center">
        <div className="flex w-1/2 items-center justify-end">Total</div>
        <div className="flex w-1/2 items-center justify-center">{total}%</div>

        {total !== 100 && isDirty && (
          <div className="w-4/5 text-right text-red-500">Total percentage must be equal to 100%</div>
        )}
      </div>
    </FormSection>
  );
};

export const DeliveryTime = ({ form }: { form: UseFormReturn<MerchantApplicationFormSchema> }) => {
  const total =
    form.watch('service_deliveries.more_than_one_month_delivery') +
    form.watch('service_deliveries.one_month_delivery') +
    form.watch('service_deliveries.one_week_delivery') +
    form.watch('service_deliveries.same_day_delivery') +
    form.watch('service_deliveries.two_week_delivery');

  const isDirty =
    form.formState.dirtyFields.service_deliveries?.more_than_one_month_delivery ||
    form.formState.dirtyFields.service_deliveries?.one_month_delivery ||
    form.formState.dirtyFields.service_deliveries?.one_week_delivery ||
    form.formState.dirtyFields.service_deliveries?.same_day_delivery ||
    form.formState.dirtyFields.service_deliveries?.two_week_delivery;

  return (
    <FormSection id="delivery-time" title="Delivery Time">
      <FormField
        control={form.control}
        name="service_deliveries.same_day_delivery"
        render={({ field }) => (
          <PercentInput field={field} label="At Time of Payment" data-testid="delivery-time-same-day-input" />
        )}
      />

      <FormField
        control={form.control}
        name="service_deliveries.one_week_delivery"
        render={({ field }) => (
          <PercentInput field={field} label="1-7 Days" data-testid="delivery-time-one-week-input" />
        )}
      />

      <FormField
        control={form.control}
        name="service_deliveries.two_week_delivery"
        render={({ field }) => (
          <PercentInput field={field} label="8-14 Days" data-testid="delivery-time-two-week-input" />
        )}
      />

      <FormField
        control={form.control}
        name="service_deliveries.one_month_delivery"
        render={({ field }) => (
          <PercentInput field={field} label="15-30 Days" data-testid="delivery-time-one-month-input" />
        )}
      />

      <FormField
        control={form.control}
        name="service_deliveries.more_than_one_month_delivery"
        render={({ field }) => (
          <PercentInput field={field} label="Over 30 Days" data-testid="delivery-time-over-month-input" />
        )}
      />

      <div className="flex w-full flex-wrap items-center">
        <div className="flex w-1/2 items-center justify-end">Total</div>
        <div className="flex w-1/2 items-center justify-center">{total}%</div>

        {total !== 100 && isDirty && (
          <div className="w-4/5 text-right text-red-500">Total percentage must be equal to 100%</div>
        )}
      </div>
    </FormSection>
  );
};

export type MerchantApplicationFormErrorMessageProps = {
  errorMessage: string;
  errorsByLabel: Record<string, (FieldError & { path: string[] })[]>;
  hasErrors: boolean;
};

export type MerchantApplicationFormErrorsProps = {
  children: ({ errorMessage, errorsByLabel, hasErrors }: MerchantApplicationFormErrorMessageProps) => React.ReactNode;
};

export const MerchantApplicationFormErrors = ({ children }: MerchantApplicationFormErrorsProps) => {
  const { form } = useMerchantApplicationForm();
  const errors = flattenErrors(form.formState.errors);
  const hasErrors = errors.length > 0;
  const errorsByLabel = errors.reduce(
    (acc, curr) => {
      const label = getErrorLabel(curr.path);
      if (!label) return acc;

      if (label in acc && Array.isArray(acc[label])) {
        acc[label].push(curr);
      } else {
        acc[label] = [curr];
      }
      return acc;
    },
    {} as Record<string, (FieldError & { path: string[] })[]>,
  );

  const errorMessage =
    errors.length === 1
      ? 'There is 1 validation error to correct before you can continue.'
      : `There are ${errors.length} validation errors to correct before you can continue.`;

  return children({ errorMessage, errorsByLabel, hasErrors });
};

export const MerchantApplicationFormErrorMessage = ({
  hasErrors,
  errorMessage,
  errorsByLabel,
}: MerchantApplicationFormErrorMessageProps) => {
  if (!hasErrors) return null;

  return (
    <div className="p-4 ">
      <div className="flex font-bold text-red-600 dark:text-red-400">
        <TriangleAlert className="mr-1 size-4 shrink-0" />
        {errorMessage}
      </div>

      <ul className="list-inside list-disc px-2">
        {Object.entries(errorsByLabel).map(([key, labelErrors]) => (
          <li key={`error-list-${key}`} className="select-none text-xs font-semibold text-red-500">
            {key}
            <ul className="list-inside list-disc pl-4">
              {labelErrors.map((error, index) => (
                <li
                  key={`error-${index}`}
                  onClick={() => {
                    error.ref?.focus?.();
                  }}
                  className="cursor-pointer select-none text-xs font-semibold text-red-500 underline"
                >
                  {error.message}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
};

export const MerchantApplicationFormContent = () => {
  const { form, user } = useMerchantApplicationForm();
  const isTenant = hasGroups(user, ['TENANT_ACCOUNTING', 'TENANT_UNDERWRITER', 'TENANT_ADMIN', 'TENANT_ANALYST']);
  const canEditProcessingPlan = isTenant || (user.partner_settings?.able_override_processing_plan ?? false);

  return (
    <div className="relative flex size-full flex-wrap-reverse gap-10 scroll-smooth p-8 text-xs text-gray-600 xl:flex-nowrap">
      {/* Sidebar Navigation */}
      <MerchantApplicationFormSidebar showAgreement={false} />

      {/* Main Form Area */}
      <div className="w-full flex-1">
        <MerchantApplicationFormErrors>
          {({ errorMessage, errorsByLabel, hasErrors }) => (
            <MerchantApplicationFormErrorMessage
              errorMessage={errorMessage}
              errorsByLabel={errorsByLabel}
              hasErrors={hasErrors}
            />
          )}
        </MerchantApplicationFormErrors>
        <div className="space-y-4 pb-20">
          {/* Merchant Information */}
          <section className="space-y-8">
            <h2 className="mb-4 border-b pb-2 text-lg font-bold text-foreground">Merchant Information</h2>

            {/* Merchant Details */}
            <MerchantDetails form={form} user={user} />

            {/* Contact Information */}
            <ContactInformation form={form} />

            {/* Owners */}
            <Owners form={form} canEdit={true} />

            {/* Sales Details */}
            <SalesDetails form={form} />

            {/* Partner Processing Plan */}
            <PartnerProcessing form={form} canEdit={canEditProcessingPlan} />

            {/* Payment Volume */}
            <PaymentVolume form={form} />

            {/* Payment Amounts */}
            <PaymentAmounts form={form} />

            {/* Payment Mix */}
            <PaymentMix form={form} />

            {/* Delivery Time */}
            <DeliveryTime form={form} />
          </section>
        </div>
      </div>
    </div>
  );
};

export const getErrorPath = (path: string) => {
  if (path.startsWith('processing_plan_data.')) {
    return path.replace('processing_plan_data.', 'plan.');
  }

  return path;
};

const DEFAULT_VALUES = {
  name: '',
  address: {
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    zip_code: '',
    country: '',
  },
  company: {
    legal_name: '',
    dba_name: '',
    description: '',
    tax_id: {
      mask: '',
      value: '',
    },
    phone_number: '',
    website: '',
  },
  industry_volume: {
    avg_annual_volume: 0,
    avg_ach_annual_volume: 0,
    avg_ticket_amount: 0,
    avg_ach_ticket_amount: 0,
    high_ticket_amount: 0,
    ach_high_ticket_amount: 0,
  },
  transaction_modes: {
    in_person: 0,
    online: 0,
  },
  owners: [],
  service_deliveries: {
    same_day_delivery: 0,
    one_week_delivery: 0,
    two_week_delivery: 0,
    one_month_delivery: 0,
    more_than_one_month_delivery: 0,
  },
  plan_id: '',
  plan: undefined,
  sales_code_id: '',
  sales_agent_id: '',
};

export type MerchantApplicationFormState = {
  dirtyFields: FormState<MerchantApplicationFormSchema>['dirtyFields'];
  defaultValues: FormState<MerchantApplicationFormSchema>['defaultValues'];
};

export type MerchantApplicationFormContext = {
  form: UseFormReturn<MerchantApplicationFormSchema>;
  handleSubmit: ReturnType<UseFormHandleSubmit<MerchantApplicationFormSchema>>;
  transition: boolean;
  user: AuthUser;
};

export const MerchantApplicationFormContext = React.createContext<MerchantApplicationFormContext | undefined>(
  undefined,
);

export type MerchantApplicationFormProps = UseHookFormProps<MerchantApplicationFormSchema, MerchantApplicationRow> & {
  user: AuthUser;
  children: React.ReactNode | ((context: MerchantApplicationFormContext) => React.ReactNode);
};

export const MerchantApplicationForm = ({ defaultValues, user, children, ...props }: MerchantApplicationFormProps) => {
  const { form, handleSubmit, transition } = useHookForm<MerchantApplicationFormSchema, MerchantApplicationRow>({
    resolver: zodResolver(MerchantApplicationFormSchema),
    defaultValues: Object.assign({}, DEFAULT_VALUES, defaultValues) as MerchantApplicationFormSchema,
    ...props,
  });

  const context = React.useMemo(
    () => ({ form, handleSubmit, transition, user }),
    [form, handleSubmit, transition, user],
  );

  return (
    <MerchantApplicationFormContext.Provider value={context}>
      <Form {...form}>{typeof children === 'function' ? children(context) : children}</Form>
    </MerchantApplicationFormContext.Provider>
  );
};

export const useMerchantApplicationForm = () => {
  const context = React.useContext(MerchantApplicationFormContext);
  if (!context) {
    throw new Error('useMerchantApplicationForm must be used within a MerchantApplicationFormContextProvider');
  }

  return context;
};
