import {
  CreateMerchantApplicationCompanySchema,
  CreateMerchantApplicationSchema,
  MerchantApplicationRow,
} from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import { PartnerProcessingPlanRow } from '@dbd/reporting-partners/partner.types.js';
import { fromCents } from '@dbd/ui/lib/formatters';
import { ProcessingPlanFormSchema } from '@dbd/ui-partners/schemas/processing-plan-form-schema';
import { MerchantAccountOwnershipType } from '@dbd/zod-types';
import {
  Address,
  EmployerIdentificationNumber,
  emptyObject,
  emptyString,
  MerchantApplicationId,
  SocialSecurityNumber,
} from '@dbd/zod-types-common';
import { z } from 'zod';

import { MerchantOwnerFormSchema } from './merchant-owner-form-schema.js';

export const literalEmpty = z.literal('', { errorMap: () => ({ message: '' }) }).optional();

export const CompanyFormSchema = CreateMerchantApplicationCompanySchema.extend({
  tax_id: z
    .object({
      mask: z.string(),
      value: z.string(),
    })
    .optional(),
});
export type CompanyFormSchema = z.infer<typeof CompanyFormSchema>;

export const DefaultCompanySchema = z.object({
  tax_id: z
    .object({
      mask: z.string(),
      value: z.string(),
    })
    .optional(),
  legal_name: z.string(),
  dba_name: z.string(),
  description: z.string(),
  business_start_date: z.string(),
  ownership_type: z.string(),
  category: z.string(),
  mcc: z.string(),
  website: z.string(),
  phone_number: z.string(),
  stock_symbol: z.string(),
});
export type DefaultCompanySchema = z.infer<typeof DefaultCompanySchema>;

export const CreateAddressSchema = z.union([
  z
    .object({
      address_line1: emptyString,
      address_line2: emptyString,
      city: emptyString,
      state: emptyString,
      zip_code: emptyString,
      country: emptyString,
    })
    .nullish(),
  Address,
]);
export type CreateAddressSchema = z.infer<typeof CreateAddressSchema>;

export const CreateCompanySchema = z.union([DefaultCompanySchema, CompanyFormSchema]);
export type CreateCompanySchema = z.infer<typeof CreateCompanySchema>;

export const CreateIndustryVolumeSchema = z.object({
  avg_annual_volume: z.preprocess(
    (value) => {
      if (value === '' || value === undefined || value === null || Number(value) === 0) return undefined;
      return Number(value);
    },
    z
      .number()
      .int()
      .min(1000, { message: 'Average Annual Volume cannot be lower than $1,000 and higher than $999,999,999.' })
      .max(*********, { message: 'Average Annual Volume cannot be lower than $1,000 and higher than $999,999,999.' })
      .optional(),
  ),
  avg_ach_annual_volume: z.preprocess(
    (value) => {
      if (value === '' || value === undefined || value === null || Number(value) === 0) return undefined;
      return Number(value);
    },
    z
      .number()
      .int()
      .min(1000, { message: 'Average Annual Volume cannot be lower than $1,000 and higher than $999,999,999.' })
      .max(*********, { message: 'Average Annual Volume cannot be lower than $1,000 and higher than $999,999,999.' })
      .optional(),
  ),
  avg_ticket_amount: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  high_ticket_amount: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  avg_ach_ticket_amount: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  ach_high_ticket_amount: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
});
export type CreateIndustryVolumeSchema = z.infer<typeof CreateIndustryVolumeSchema>;

export const CreateTransactionModesSchema = z.object({
  in_person: z.preprocess((value) => (Number.isNaN(Number(value)) ? 0 : Number(value)), z.number().int().min(0)),
  online: z.preprocess((value) => (Number.isNaN(Number(value)) ? 0 : Number(value)), z.number().int().min(0)),
});
export type CreateTransactionModesSchema = z.infer<typeof CreateTransactionModesSchema>;

export const CreateServiceDeliveriesSchema = z.object({
  same_day_delivery: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  one_week_delivery: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  two_week_delivery: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  one_month_delivery: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
  more_than_one_month_delivery: z.preprocess(
    (value) => (Number.isNaN(Number(value)) ? 0 : Number(value)),
    z.number().int().min(0),
  ),
});
export type CreateServiceDeliveriesSchema = z.infer<typeof CreateServiceDeliveriesSchema>;

export const MerchantApplicationFormSchema = CreateMerchantApplicationSchema.extend({
  id: MerchantApplicationId.nullish(),
  address: emptyObject(Address.optional()),
  company: CreateCompanySchema,
  industry_volume: CreateIndustryVolumeSchema,
  transaction_modes: CreateTransactionModesSchema,
  service_deliveries: CreateServiceDeliveriesSchema,
  owners: z.array(MerchantOwnerFormSchema),
  plan: ProcessingPlanFormSchema.optional(),
}).superRefine((formData, ctx) => {
  const ownershipType = MerchantAccountOwnershipType.safeParse(formData.company?.ownership_type);
  const taxIdLength = formData.company.tax_id?.value?.length ?? 0;
  if (ownershipType.success && ownershipType.data === 'SOLO_TRADER') {
    if (
      taxIdLength > 0 &&
      taxIdLength <= 9 &&
      SocialSecurityNumber.safeParse(formData.company.tax_id?.value).success === false
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid SSN',
        path: ['company', 'tax_id'],
      });
    }
  } else if (ownershipType.success) {
    if (
      taxIdLength > 0 &&
      taxIdLength <= 9 &&
      EmployerIdentificationNumber.safeParse(formData.company.tax_id?.value).success === false
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid EIN',
        path: ['company', 'tax_id'],
      });
    }
  }
});

export type MerchantApplicationFormSchema = z.infer<typeof MerchantApplicationFormSchema>;

export const getPlanSchemaFromApplicationRow = (
  application: MerchantApplicationRow,
  plan?: PartnerProcessingPlanRow,
) => {
  return ProcessingPlanFormSchema.parse({
    id: plan?.id ?? application.partner_processing_plan_id,
    type: application.processing_plan_data?.plan_type,
    interchange_cost: application.processing_plan_data?.interchange_cost,
    description: plan?.description,
    ach_enabled: application.processing_plan_data?.ach_fees?.ach_enabled,
    ach_bps: application.processing_plan_data?.ach_fees?.ach_bps,
    ach_txn_fee: application.processing_plan_data?.ach_fees?.ach_txn_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.ach_txn_fee)
      : undefined,
    ach_max_fee: application.processing_plan_data?.ach_fees?.ach_max_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.ach_max_fee)
      : undefined,
    ach_reject_fee: application.processing_plan_data?.ach_fees?.reject_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.reject_fee)
      : undefined,
    ach_reject_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.ach_reject,
    ach_reversal_fee: application.processing_plan_data?.ach_fees?.reversal_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.reversal_fee)
      : undefined,
    ach_reversal_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.ach_reversal,
    bank_linking_fee: application.processing_plan_data?.ach_fees?.linking_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.linking_fee)
      : undefined,
    ach_linking_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.ach_linking,
    same_day_ach_bps: application.processing_plan_data?.ach_fees?.same_day_ach_bps,
    same_day_ach_txn_fee: application.processing_plan_data?.ach_fees?.same_day_ach_txn_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.same_day_ach_txn_fee)
      : undefined,
    same_day_ach_max_fee: application.processing_plan_data?.ach_fees?.same_day_ach_max_fee
      ? fromCents(application.processing_plan_data?.ach_fees?.same_day_ach_max_fee)
      : undefined,
    batch_fee: application.processing_plan_data?.fees?.batch
      ? fromCents(application.processing_plan_data?.fees?.batch)
      : undefined,
    batch_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.batch,
    auth_cnp_fee: application.processing_plan_data?.auth_fee?.cnp
      ? fromCents(application.processing_plan_data?.auth_fee?.cnp)
      : undefined,
    auth_cp_fee: application.processing_plan_data?.auth_fee?.cp
      ? fromCents(application.processing_plan_data?.auth_fee?.cp)
      : undefined,
    application_fee: application.processing_plan_data?.fees?.application
      ? fromCents(application.processing_plan_data?.fees?.application)
      : undefined,
    application_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.application,
    chargeback_fee: application.processing_plan_data?.fees?.chargeback
      ? fromCents(application.processing_plan_data?.fees?.chargeback)
      : undefined,
    chargeback_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.chargeback,
    dda_reject_fee: application.processing_plan_data?.fees?.dda_reject
      ? fromCents(application.processing_plan_data?.fees?.dda_reject)
      : undefined,
    dda_reject_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.dda_reject,
    early_cancellation_fee: application.processing_plan_data?.fees?.early_cancellation
      ? fromCents(application.processing_plan_data?.fees?.early_cancellation)
      : undefined,
    early_cancellation_fee_paid_by_partner:
      application.processing_plan_data?.fees_paid_by_partner?.early_cancellation ??
      plan?.early_cancellation_fee_paid_by_partner,
    gateway_fee: application.processing_plan_data?.fees?.gateway
      ? fromCents(application.processing_plan_data?.fees?.gateway)
      : undefined,
    gateway_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.gateway,
    monthly_minimum_fee: application.processing_plan_data?.fees?.monthly_minimum
      ? fromCents(application.processing_plan_data?.fees?.monthly_minimum)
      : undefined,
    monthly_minimum_fee_paid_by_partner:
      application.processing_plan_data?.fees_paid_by_partner?.monthly_minimum ??
      plan?.monthly_minimum_fee_paid_by_partner,
    pci_non_compliance_fee: application.processing_plan_data?.fees?.pci_non_compliance
      ? fromCents(application.processing_plan_data?.fees?.pci_non_compliance)
      : undefined,
    pci_non_compliance_fee_paid_by_partner:
      application.processing_plan_data?.fees_paid_by_partner?.pci_non_compliance ??
      plan?.pci_non_compliance_fee_paid_by_partner,
    platform_fee: application.processing_plan_data?.fees?.platform
      ? fromCents(application.processing_plan_data?.fees?.platform)
      : undefined,
    platform_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.platform,
    regulatory_product_fee: application.processing_plan_data?.fees?.regulatory_product
      ? fromCents(application.processing_plan_data?.fees?.regulatory_product)
      : undefined,
    regulatory_product_fee_paid_by_partner:
      application.processing_plan_data?.fees_paid_by_partner?.regulatory_product ??
      plan?.regulatory_product_fee_paid_by_partner,
    retrieval_fee: application.processing_plan_data?.fees?.retrieval
      ? fromCents(application.processing_plan_data?.fees?.retrieval)
      : undefined,
    retrieval_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.retrieval,
    token_fee: application.processing_plan_data?.fees?.token
      ? fromCents(application.processing_plan_data?.fees?.token)
      : undefined,
    token_fee_paid_by_partner: application.processing_plan_data?.fees_paid_by_partner?.token,
    merchant_contract_description: application.processing_plan_data?.merchant_contract_description,
    partner_pays_chargeback_amount: application.processing_plan_data?.partner_pays_chargeback_amount,
    comment: application.processing_plan_data?.comment ?? '',
    cards_accepted: application.processing_plan_data?.card_acceptances?.map((card) => ({
      card_type: card.card_type,
      cp_bps: card.discount_fee.cp_bps,
      cnp_bps: card.discount_fee.cnp_bps,
      state: card.state,
    })),
  });
};
