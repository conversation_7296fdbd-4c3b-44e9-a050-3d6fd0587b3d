/*
  This file contains the zod schemas for the DTO types.
  These types should be snake_cased and should be used in the DTO layer and persistence layer.
 */
import { FeeSourceEnum, FeeSubTypeEnum, FeeTypeEnum } from '@dbd/fee-service-types';
import {
  ACHPaymentServiceTypeEnum,
  AchSettlementTransferId,
  AdjustmentId,
  ApiKeyId,
  ApplicationEventId,
  AuthUserSelfSubType,
  AuthUserSelfType,
  BankAccountId,
  BankFundingBatchId,
  BankGatewaySettingId,
  BankTransferId,
  BusinessId,
  CheckoutSessionId,
  ContractorId,
  ContractorSettingsId,
  CrossRiverBankRecordId,
  DisputeAttachmentId,
  DisputeId,
  DisputeNoteId,
  EnterpriseId,
  EntityType,
  EntityTypeEnum,
  FeeId,
  FileId,
  FundingId,
  GatewaySettingId,
  LedgerAccountBalanceId,
  LedgerAccountId,
  LedgerAccountOwnerTypeEnum,
  MerchantAccountId,
  MerchantApplicationId,
  NegativeListEntryId,
  NoteId,
  PartnerId,
  PartnerSettingsId,
  PaymentId,
  PaymentIntentId,
  PaymentMethodId,
  PaymentSettingId,
  PayoutId,
  PayoutJobId,
  ProcessingPlanCardBrandEnum,
  ProcessingPlanSettingId,
  ProcessorSettlementId,
  RefundFailureId,
  RefundId,
  ReversalId,
  SettlementId,
  SettlementReportId,
  SettlementStatusEnum,
  SnowflakeExportRecordId,
  TenantId,
  TransactionId,
  TransferId,
  UnmatchedSettlementId,
  UserId,
} from '@dbd/zod-types-common';
import { z } from 'zod';

import { FeeSourceId, PaymentIntentFeeSourceId, PayoutOwnerId, TransactionSourceId } from './domain.js';
import {
  ACHPaymentAccountType,
  ACHPaymentDirection,
  ACHPaymentStatus,
  ACHPaymentType,
  AchSettlementTransferStatus,
  AVSCheckType,
  AVSCodeType,
  BankAccountOwnerType,
  BankGatewayType,
  BankTransferStatus,
  BankType,
  CardBrand,
  CardWallet,
  ContractorParentType,
  CrBankRecordType,
  CVVCheckType,
  CVVCodeType,
  DisputeStatus,
  EntryMode,
  EventType,
  FileBusinessPurpose,
  FileDocumentType,
  FileSourceType,
  FileSupportedMimeType,
  FundingType,
  GatewayType,
  NoteSourceType,
  OwnerType,
  PaymentCardNotPresentType,
  PaymentInitiatedByType,
  PaymentIntentStatus,
  PaymentMethodType,
  PaymentSettingPayoutsSecCode,
  PaymentStatus,
  PayoutStatus,
  ProcessingPlanInterchangeCost,
  ProcessingPlanState,
  ProcessingPlanType,
  RefundStatus,
  SettlementReportStatus,
  TokenSourceType,
  TransactionRootSourceType,
  TransactionSourceType,
  TransferSourceType,
} from './enums.js';

const map = (obj: any, src: string, dst: string) => {
  if (typeof obj === 'object' && obj !== null && src in obj) {
    (obj as Record<string, any>)[dst] = obj[src];
    delete obj[src];
  }

  return obj;
};
export const noNulls = <T extends z.ZodTypeAny>(type: T) =>
  z.preprocess((val) => (val === null ? undefined : val), type);
const dtString = z.preprocess(
  (val) => (typeof val === 'number' ? new Date(val).toISOString() : val),
  z.string().datetime({ offset: true }),
);

export const ListMetaDTO = z.object({
  has_more: z.boolean(),
  limit: z.number(),
});
export type ListMetaDTO = z.infer<typeof ListMetaDTO>;

export const PaymentOrderDetailItem = z.object({
  line_number: noNulls(z.number().int().optional()),
  material: noNulls(z.string().optional()),
  description: noNulls(z.string().optional()),
  upc: noNulls(z.number().int().optional()),
  quantity: noNulls(z.number().int().optional()),
  uom: noNulls(z.string().optional()),
  unit_cost: noNulls(z.number().int().optional()),
  net_amount: noNulls(z.number().int().optional()),
  tax_amount: noNulls(z.number().int().optional()),
  tip_amount: noNulls(z.number().int().optional()),
  surcharge_amount: noNulls(z.number().int().optional()),
  tax_exempt: noNulls(z.boolean().optional()),
  discount_amount: noNulls(z.number().int().optional()),
});
export type PaymentOrderDetailItem = z.infer<typeof PaymentOrderDetailItem>;

export const PaymentSplit = z.object({
  account_id: z.string(), // PartnerId,
  amount: z.number().gte(0),
  description: z.string(),
});
export type PaymentSplit = z.infer<typeof PaymentSplit>;

export const PaymentOrderDetails = z.object({
  card_not_present_type: PaymentCardNotPresentType.optional(),
  po_number: noNulls(z.string().optional()),
  tax_exempt: noNulls(z.boolean().optional()),
  tax_amount: noNulls(z.number().optional()),
  tip_amount: noNulls(z.number().optional()),
  surcharge_amount: noNulls(z.number().optional()),
  freight_amount: noNulls(z.number().optional()),
  duty_amount: noNulls(z.number().optional()),
  initiated_by: noNulls(PaymentInitiatedByType.optional()),
  is_scheduled_payment: noNulls(z.boolean().optional()),
  order_date: noNulls(z.string().optional()),
  ship_to_zip: noNulls(z.string().optional()),
  ship_from_zip: noNulls(z.string().optional()),
  ship_to_country: noNulls(z.string().optional()),
  invoice_id: noNulls(z.string().optional()),
  items: noNulls(z.array(PaymentOrderDetailItem).optional()),
});
export type PaymentOrderDetails = z.infer<typeof PaymentOrderDetails>;

export const PaymentSignature = z.object({
  data: z.string(),
  format: noNulls(z.enum(['bmp', 'png', 'jpg']).optional()),
  dimensions: z
    .object({
      width: z.coerce.number().int(),
      height: z.coerce.number().int(),
    })
    .optional(),
});
export type PaymentSignature = z.infer<typeof PaymentSignature>;

export const GatewayResponseStatusType = z.enum(['succeeded', 'failed']);
// TODO: This needs to be fleshed out..
// export const GatewayAuthResponse = z.record(z.string(), z.any());

export const Transaction = z.object({
  id: TransactionId,
  entity: z.literal(EntityType.Transaction),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  ledger_account_id: LedgerAccountId,
  payout_id: noNulls(PayoutId.optional()),
  bank_transfer_id: noNulls(BankTransferId.optional()),
  source_type: TransactionSourceType,
  source_id: TransactionSourceId,
  source_account_id: noNulls(MerchantAccountId.optional()),
  amount: z.coerce.number().int(),
  root_source_id: noNulls(TransactionSourceId.optional()), //TODO: Remove optional once backfill is complete
  root_source_type: noNulls(TransactionRootSourceType.optional()), //TODO: Remove optional once backfill is complete
  settlement_id: SettlementId,
  settlement_status: SettlementStatusEnum,
  settled_at: noNulls(z.string().optional()),
  description: noNulls(z.string().optional()),
});
export type Transaction = z.infer<typeof Transaction>;

export const ApiKey = z.object({
  id: ApiKeyId,
  entity: z.literal(EntityType.ApiKey),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  api_key: z.string(),
  is_public: z.boolean(),
  is_test: z.boolean(),
  name: z.string(),
  email: z.string(),
  permissions: z.array(z.string()),
});
export type ApiKey = z.infer<typeof ApiKey>;

export const Adjustment = z.object({
  id: AdjustmentId,
  entity: z.literal(EntityType.Adjustment),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  to_account_id: LedgerAccountId,
  from_account_id: LedgerAccountId,
  amount: z.number().int(),
  description: z.string(),
});
export type Adjustment = z.infer<typeof Adjustment>;

export const TimeSeriesDataBucket = z.object({
  amount: z.number().int(),
  start_date: z.union([z.string().datetime({ offset: true }), z.string().date()]),
  end_date: z.union([z.string().datetime({ offset: true }), z.string().date()]),
});
export type TimeSeriesDataBucket = z.infer<typeof TimeSeriesDataBucket>;

export const PaymentIntentSummary = z.object({
  count: z.coerce.number().int(),
  amount: z.coerce.number().int(),
  amount_captured: z.coerce.number().int(),
  status: PaymentIntentStatus,
});
export type PaymentIntentSummary = z.infer<typeof PaymentIntentSummary>;

export const PayoutTransactionSummary = z.object({
  amount: z.number().int(),
  count: z.number().int(),
  type: TransactionSourceType,
});
export type PayoutTransactionSummary = z.infer<typeof PayoutTransactionSummary>;

export const PayoutCrossRiverData = z.object({
  id: z.string(),
  trace_number: noNulls(z.string().optional()),
  service_type: ACHPaymentServiceTypeEnum,
  description: z.string(),
  bank_account_number_mask: noNulls(z.string().optional()),
  bank_account_type: ACHPaymentAccountType,
  bank_account_id: noNulls(z.string().optional()),
  status: ACHPaymentStatus,
  payment_type: ACHPaymentType,
  direction: ACHPaymentDirection,
  reason_code: noNulls(z.string().optional()),
  reason_data: noNulls(z.string().optional()),
  updated_at: z.string().datetime({ offset: true }),
  type: z.literal(BankType.enum.CrossRiverBank),
  reference_id: noNulls(z.string().optional()),
  sec_code: z.string(),
});
export type PayoutCrossRiverData = z.infer<typeof PayoutCrossRiverData>;

export const PayoutPaymentData = PayoutCrossRiverData;
export type PayoutPaymentData = z.infer<typeof PayoutPaymentData>;

export const Payout = z.object({
  id: PayoutId,
  entity: z.literal(EntityType.Payout),
  status: PayoutStatus,
  payout_number: z.coerce.number().int().gte(0),
  transaction_count: z.coerce.number().int().gte(0),
  transaction_summary: noNulls(z.array(PayoutTransactionSummary).optional()),
  amount: z.coerce.number().int(),
  net_amount: z.coerce.number().int(),
  fee_amount: z.coerce.number().int(),
  batch_fee: z.coerce.number().int(),
  reject_fee: z.coerce.number().int(),
  latest_payment_data: noNulls(PayoutPaymentData.optional()),
  payment_data: noNulls(z.array(PayoutPaymentData).optional()),
  payment_attempt_count: z.coerce.number().int().gte(0),
  payment_reject_count: z.coerce.number().int().gte(0),

  owner_type: LedgerAccountOwnerTypeEnum,
  owner_id: PayoutOwnerId,
  business_id: noNulls(BusinessId.optional()),

  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(dtString.optional()),

  created_at: dtString,
  updated_at: dtString,
  completed_at: noNulls(dtString.optional()),
  returned_at: noNulls(dtString.optional()),
});
export type Payout = z.infer<typeof Payout>;

export const CrossRiverBankRecord = z.object({
  id: CrossRiverBankRecordId,
  x_id: z.string(),
  record_type: CrBankRecordType,
  data: z.any(),
});
export type CrossRiverBankRecord = z.infer<typeof CrossRiverBankRecord>;

export const Fee = z.object({
  id: FeeId,
  entity: z.literal(EntityType.Fee),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  fee_type: FeeTypeEnum,
  fee_sub_type: noNulls(FeeSubTypeEnum),
  description: noNulls(z.string().optional()),
  from_owner_id: noNulls(z.string().optional()),
  from_owner_type: noNulls(LedgerAccountOwnerTypeEnum.optional()),
  to_owner_id: noNulls(z.string().optional()),
  to_owner_type: noNulls(LedgerAccountOwnerTypeEnum.optional()),
  source_type: noNulls(FeeSourceEnum.optional()),
  source_id: noNulls(FeeSourceId.optional()),
  source_account_id: noNulls(z.string().optional()),
  amount: z.number().int().gte(0),
  auth_fee: noNulls(z.number().int().gte(0).default(0)),
  card_fee_bps: z.number().int().gte(0).default(0),
  ach_per_transaction_fee: z.number().int().gte(0).default(0),
  ach_fee_bps: z.number().int().gte(0).default(0),
  ach_bps_fee_max: z.number().int().gte(0).default(0),
  ach_service_type: noNulls(ACHPaymentServiceTypeEnum.optional()),
  card_type: noNulls(ProcessingPlanCardBrandEnum.optional()),
  settlement_id: SettlementId,
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().optional()),
  monthly_fee_date: noNulls(z.string().optional()),
});
export type Fee = z.infer<typeof Fee>;

export const FundingRecord = z.object({
  snowflake_row_hash: z.string(),
  merchant_id: z.string(),
  amount: z.number(),
  debit_amount: z.number(),
  credit_amount: z.number(),
  description: z.string(),
  dda_last_4: z.string().nullish(),
  dba_name: z.string().nullish(),
  record_date: z.string().date(),
  funding_date: z.string().date(),
  batch_date: z.string().date(),
  deposit_type_code: z.coerce.number().int(),
  deposit_type_description: z.string(),
  category_major_code: z.coerce.number().int(),
  category_major_description: z.string().nullish(),
  category_minor_code: z.coerce.number().int(),
  category_minor_description: z.string().nullish(),
  authorization_network_code: z.coerce.number().int(),
  authorization_network_description: z.string(),
});
export type FundingRecord = z.infer<typeof FundingRecord>;

export const DisputeNote = z.object({
  id: DisputeNoteId,
  message: z.string(),
  created_at: z.string().datetime({ offset: true }),
  user_id: UserId,
});
export type DisputeNote = z.infer<typeof DisputeNote>;

export const DisputeAttachment = z.object({
  id: DisputeAttachmentId,
  description: z.string(),
  file_id: FileId,
  created_at: z.string().datetime({ offset: true }),
  uploaded_by: UserId,
  owner_id: z.union([MerchantAccountId, ContractorId, BusinessId, PartnerId, TenantId, EnterpriseId]),
});
export type DisputeAttachment = z.infer<typeof DisputeAttachment>;

export const DisputeReferenceMeta = z.object({
  card_last_four: noNulls(z.string().optional()),
  merchant_id: noNulls(MerchantAccountId.optional()),
  authorization_code: noNulls(z.string().optional()),
  transaction_date: noNulls(z.string().date().optional()),
  transaction_amount: z.number().int().default(0),
  retrieval_reference: noNulls(z.string().optional()),
});
export type DisputeReferenceMeta = z.infer<typeof DisputeReferenceMeta>;

export const Dispute = z.object({
  id: DisputeId,
  entity: z.literal(EntityType.Dispute),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  account_id: MerchantAccountId,
  business_id: BusinessId,
  status: DisputeStatus,
  payment_id: PaymentId,
  payment_intent_id: PaymentIntentId,
  amount: z.number().int(),
  notes: noNulls(z.array(DisputeNote).optional()),
  attachments: noNulls(z.array(DisputeAttachment).optional()),
  dispute_date: z.string().date(),
  response_due_date: z.string().date(),
  closed_date: noNulls(z.string().date().optional()),
  reason_disputed_code: z.number().int(),
  reason_disputed: z.string(),
  dispute_response: noNulls(z.string().optional()),
  reference_meta: noNulls(DisputeReferenceMeta.optional()),
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type Dispute = z.infer<typeof Dispute>;

export const Funding = z.object({
  id: FundingId,
  entity: z.literal(EntityType.Funding),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),

  snowflake_row_hash: z.string(),
  merchant_id: MerchantAccountId,
  funding_type: FundingType,
  amount: z.number().int(),
  debit_amount: z.number().int(),
  credit_amount: z.number().int(),
  description: z.string(),
  dda_last_four: z.string(),
  dba_name: z.string(),
  record_date: z.string().date(),
  funding_date: z.string().date(),
  batch_date: z.string().date(),
  deposit_type_code: z.number().int().gte(0),
  deposit_type_description: z.string(),
  category_major_code: z.number().int().gte(0),
  category_major_description: z.string(),
  category_minor_code: z.number().int().gte(0),
  category_minor_description: z.string(),
  authorization_network_code: z.number().int().gte(0),
  authorization_network_description: z.string(),

  tenant_id: TenantId,
  business_id: BusinessId,
  account_id: MerchantAccountId,
  fee_id: noNulls(FeeId.optional()),
  reference_meta: noNulls(z.record(z.string(), z.any()).optional()),
  settlement_id: SettlementId,
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type Funding = z.infer<typeof Funding>;

export const LedgerAccount = z.object({
  id: LedgerAccountId,
  entity: z.literal(EntityType.LedgerAccount),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  owner_type: LedgerAccountOwnerTypeEnum,
  owner_id: z.union([MerchantAccountId, ContractorId, BusinessId, PartnerId, TenantId, EnterpriseId]),
  payouts_count: z.number().int().gte(0),
});
export type LedgerAccount = z.infer<typeof LedgerAccount>;

const ApplePaySetting = z.object({
  // Forward's Apple Pay merchant ID under which the merchant is registered with Apple Pay
  apple_pay_merchant_id: z.string(),
  merchant_name: z.string(),
});
export type ApplePaySetting = z.infer<typeof ApplePaySetting>;

export const PaymentGatewaySetting = z.object({
  id: GatewaySettingId,
  gateway_id: z.string(), // Should this be typed?
  type: GatewayType,
  merchant_id: MerchantAccountId,
  name: z.string(),
  active: noNulls(z.boolean().optional()),
  apple_pay: noNulls(ApplePaySetting.optional()),
  use_custom_credentials: noNulls(z.boolean().optional()),
  config: z
    .object({
      site: noNulls(z.string().optional()),
      user_name: noNulls(z.string().optional()),
      password: noNulls(z.string().optional()),
    })
    .optional(),
});
export type PaymentGatewaySetting = z.infer<typeof PaymentGatewaySetting>;

export const PaymentGatewaySettings = z.object({
  active_gateway: PaymentGatewaySetting,
  gateways: z.array(PaymentGatewaySetting),
});
export type PaymentGatewaySettings = z.infer<typeof PaymentGatewaySettings>;

export const BankPaymentGatewaySetting = z.object({
  id: BankGatewaySettingId,
  gateway_id: z.string(), // Should this be typed?
  merchant_id: noNulls(z.string().optional()),
  active: noNulls(z.boolean().optional()),
  type: BankGatewayType,
  payout_delay: z.number().int().gte(0),
});
export type BankPaymentGatewaySetting = z.infer<typeof BankPaymentGatewaySetting>;

export const BankGatewaySettings = z.object({
  active_gateway: BankPaymentGatewaySetting,
  gateways: z.array(BankPaymentGatewaySetting),
});
export type BankGatewaySettings = z.infer<typeof BankGatewaySettings>;

export const PaymentPayoutSettings = z.object({
  negative_balance_threshold: z.number().int(),
  positive_balance_threshold: z.number().int().gte(0),
});
export type PaymentPayoutSettings = z.infer<typeof PaymentPayoutSettings>;

export const PaymentCheckoutSettings = z.object({
  active_domain: z.string(),
  domains: z.string().array(),
});
export type PaymentCheckoutSettings = z.infer<typeof PaymentCheckoutSettings>;

export const PaymentBrandingSettings = z.object({
  primary_color: z.string(),
  company_name: z.string(),
  company_logo_url: z.string(),
});
export type PaymentBrandingSettings = z.infer<typeof PaymentBrandingSettings>;

export const PaymentProcessingPlanAcceptedCard = z.object({
  card_type: ProcessingPlanCardBrandEnum,
  discount_fee: z.object({
    card_present_bps: z.number().int(),
    card_not_present_bps: z.number().int(),
  }),
  state: ProcessingPlanState,
});
export type PaymentProcessingPlanAcceptedCard = z.infer<typeof PaymentProcessingPlanAcceptedCard>;

export const PaymentProcessingPlanAuthFee = z.object({
  card_present: z.number().int(),
  card_not_present: z.number().int(),
});
export type PaymentProcessingPlanAuthFee = z.infer<typeof PaymentProcessingPlanAuthFee>;

export const PaymentProcessingPlanFees = z.object({
  chargeback: z.number().int(),
  retrieval: z.number().int(),
  batch: z.number().int(),
  application: z.number().int(),
  monthly_minimum: z.number().int(),
  pci_non_compliance: z.number().int(),
  gateway: z.number().int(),
  early_cancellation: z.number().int(),
  regulatory_product: z.number().int(),
  dda_reject: z.number().int(),
  platform: z.number().int(),
  token: z.number().int(),
});
export type PaymentProcessingPlanFees = z.infer<typeof PaymentProcessingPlanFees>;

export const PaymentProcessingPlanFeesPaidByPartner = z.object({
  chargeback: noNulls(z.boolean().optional()),
  retrieval: noNulls(z.boolean().optional()),
  batch: noNulls(z.boolean().optional()),
  application: noNulls(z.boolean().optional()),
  monthly_minimum: noNulls(z.boolean().optional()),
  pci_non_compliance: noNulls(z.boolean().optional()),
  gateway: noNulls(z.boolean().optional()),
  early_cancellation: noNulls(z.boolean().optional()),
  regulatory_product: noNulls(z.boolean().optional()),
  dda_reject: noNulls(z.boolean().optional()),
  platform: noNulls(z.boolean().optional()),
  token: noNulls(z.boolean().optional()),

  linking_fee: noNulls(z.boolean().optional()),
  reject_fee: noNulls(z.boolean().optional()),
  reversal_fee: noNulls(z.boolean().optional()),

  ach_linking: noNulls(z.boolean().optional()),
  ach_reject: noNulls(z.boolean().optional()),
  ach_reversal: noNulls(z.boolean().optional()),
});
export type PaymentProcessingPlanFeesPaidByPartner = z.infer<typeof PaymentProcessingPlanFeesPaidByPartner>;

export const BankProcessingPlanSettings = z.object({
  fees: z.object({
    ach_per_transaction: noNulls(z.number().int().optional()),
    same_day_ach_per_transaction: noNulls(z.number().int().optional()),
    ach_bps: noNulls(z.number().int().optional()),
    same_day_ach_bps: noNulls(z.number().int().optional()),
    ach_max: noNulls(z.number().int().optional()),
    same_day_ach_max: noNulls(z.number().int().optional()),
    linking_fee: noNulls(z.number().int().optional()),
    reject_fee: noNulls(z.number().int().optional()),
    reversal_fee: noNulls(z.number().int().optional()),
  }),
  service_type: ACHPaymentServiceTypeEnum,
});
export type BankProcessingPlanSettings = z.infer<typeof BankProcessingPlanSettings>;

export const PaymentProcessingPlanSetting = z.object({
  id: ProcessingPlanSettingId,
  plan_type: ProcessingPlanType,
  effective_date: z.string().date(),
  accepted_cards: z.array(PaymentProcessingPlanAcceptedCard),
  auth_fee: PaymentProcessingPlanAuthFee,
  fees: PaymentProcessingPlanFees,
  fees_paid_by_partner: noNulls(PaymentProcessingPlanFeesPaidByPartner.optional()),
  partner_pays_chargeback_amount: noNulls(z.boolean().optional()),
  comment: z.string(),
  interchange_cost: ProcessingPlanInterchangeCost,
  bank_processing_plan_settings: noNulls(BankProcessingPlanSettings.optional()),
});
export type PaymentProcessingPlanSetting = z.infer<typeof PaymentProcessingPlanSetting>;

export const PaymentProcessingPlanSettings = z.object({
  processing_plans: z.array(PaymentProcessingPlanSetting),
});
export type PaymentProcessingPlanSettings = z.infer<typeof PaymentProcessingPlanSettings>;

export const PaymentBoardingSettings = z.object({
  mid: z.string(),
  tid: z.string(),
});
export type PaymentBoardingSettings = z.infer<typeof PaymentBoardingSettings>;

export const PaymentSettings = z.object({
  id: PaymentSettingId,
  entity: z.literal(EntityType.PaymentSetting),
  processing_enabled: z.boolean(),
  payouts_enabled: z.boolean(),
  payouts_sec_code: noNulls(PaymentSettingPayoutsSecCode.optional()),
  payouts_same_day: noNulls(z.boolean().optional()),
  bank_processing_enabled: noNulls(z.boolean().optional()),
  bank_processing_max_transaction_amount: z.number().int().gte(0).default(0),
  bank_processing_max_payment_method_pending_amount: z.number().int().gte(0).default(0),
  bank_processing_max_account_pending_amount: z.number().int().gte(0).default(0),
  payout_hold_duration_hours: z.number().int().gte(0).default(0),

  gateway_settings: noNulls(PaymentGatewaySettings.optional()),
  bank_gateway_settings: noNulls(BankGatewaySettings.optional()),
  payout_settings: noNulls(PaymentPayoutSettings.optional()),
  checkout_settings: noNulls(PaymentCheckoutSettings.optional()),
  branding_settings: noNulls(PaymentBrandingSettings.optional()),
  processing_plan_settings: noNulls(PaymentProcessingPlanSettings.optional()),
  boarding_settings: noNulls(PaymentBoardingSettings.optional()),

  merchant_name: noNulls(z.string().optional()),
  bank_payment_description: noNulls(z.string().optional()),
  widget_brand_name: noNulls(z.string().optional()),
  widget_brand_subtitle: noNulls(z.string().optional()),

  enterprise_id: EnterpriseId,
  tenant_id: TenantId,
  account_id: MerchantAccountId,
  partner_id: PartnerId,
  business_id: BusinessId,

  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
});
export type PaymentSettings = z.infer<typeof PaymentSettings>;

export const PayoutJob = z.object({
  id: PayoutJobId,
  entity: z.literal(EntityType.PayoutJob),
  status: PayoutStatus,
  task_token: z.string(),
  payout_count: z.number().int().default(0),
  amount: z.number().int().default(0),
  net_amount: z.number().int().default(0),
  fee_amount: z.number().int().default(0),
  tenant_id: TenantId,
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  completed_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type PayoutJob = z.infer<typeof PayoutJob>;

export const ProcessorSettlement = z.object({
  id: ProcessorSettlementId,
  entity: z.literal(EntityType.ProcessorSettlement),
  settlement_id: SettlementId,
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
});
export type ProcessorSettlement = z.infer<typeof ProcessorSettlement>;

export const SettlementReferenceMeta = z.object({
  card_last_four: noNulls(z.string().optional()),
  authorization_code: noNulls(z.string().optional()),
  transaction_date: noNulls(z.string().date().optional()),
  amount_captured: z.number().int().default(0),
  merchant_id: noNulls(MerchantAccountId.optional()),
  retrieval_reference: noNulls(z.string().optional()),
  invoice_number: noNulls(z.string().optional()),
});
export type SettlementReferenceMeta = z.infer<typeof SettlementReferenceMeta>;

export const Settlement = z.object({
  id: SettlementId,
  entity: z.literal(EntityType.Settlement),
  status: SettlementStatusEnum,
  source_type: EntityTypeEnum,
  source_id: z.union([
    AdjustmentId,
    ApiKeyId,
    BankTransferId,
    CheckoutSessionId,
    CrossRiverBankRecordId,
    DisputeId,
    ApplicationEventId,
    FeeId,
    FileId,
    FundingId,
    LedgerAccountId,
    LedgerAccountBalanceId,
    NoteId,
    PartnerSettingsId,
    PaymentId,
    PaymentIntentId,
    PaymentMethodId,
    PaymentSettingId,
    PayoutId,
    PayoutJobId,
    ProcessorSettlementId,
    RefundId,
    ReversalId,
    RefundFailureId,
    SettlementId,
    SettlementReportId,
    SnowflakeExportRecordId,
    TransactionId,
    TransferId,
    UnmatchedSettlementId,
    ContractorSettingsId,
    NegativeListEntryId,
    AchSettlementTransferId,
    BankFundingBatchId,
  ]),
  reference_key: z.string(),
  reference: z.string(),
  reference_meta: noNulls(SettlementReferenceMeta.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
  processor_settlement_id: noNulls(z.string().optional()),
  forced: noNulls(z.boolean().optional()),
});
export type Settlement = z.infer<typeof Settlement>;

export const SettlementDuplicateExternalRecord = z.object({
  retrieval_reference: z.string(),
  funded_date: z.string().date(),
  settlement_date: z.string().date(),
});
export type SettlementDuplicateExternalRecord = z.infer<typeof SettlementDuplicateExternalRecord>;

export const SettlementDuplicate = z.object({
  settlement_ids: z.string(SettlementId),
  external_records: z.array(SettlementDuplicateExternalRecord),
});
export type SettlementDuplicate = z.infer<typeof SettlementDuplicate>;

export const SettlementReport = z.object({
  id: SettlementReportId,
  entity: z.literal(EntityType.SettlementReport),
  created_at: z.string().datetime({ offset: true }),
  start_date: z.string().date(),
  end_date: z.string().date(),
  updated_at: z.string().datetime({ offset: true }),
  completed_at: noNulls(z.string().datetime({ offset: true }).optional()),
  tenant_id: TenantId,
  status: SettlementReportStatus,
  expected_settlement_file_count: z.number().int().gte(0),
  processed_settlement_file_count: z.number().int().gte(0),
  failed_settlement_file_count: z.number().int().gte(0),
  expected_funding_file_count: z.number().int().gte(0),
  processed_funding_file_count: z.number().int().gte(0),
  failed_funding_file_count: z.number().int().gte(0),
  failed_files: z.array(z.string()),
  processed_files: z.array(z.string()),
  matched_settlement_count: z.number().int().gte(0),
  unmatched_settlement_count: z.number().int().gte(0),
  success_settlement_count: z.number().int().gte(0),
  failed_settlement_count: z.number().int().gte(0),
  failed_settlement_ids: noNulls(z.array(z.string()).optional()),
  duplicates: noNulls(z.array(SettlementDuplicate).optional()),
  total_settlement_amount: z.number().int(),
  total_funded_credit_amount: z.number().int(),
  total_funded_debit_amount: z.number().int(),
});
export type SettlementReport = z.infer<typeof SettlementReport>;

export const Transfer = z.object({
  id: TransferId,
  entity: z.literal(EntityType.Transfer),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  to_account_id: z.union([
    LedgerAccountId,
    ContractorId,
    MerchantAccountId,
    BusinessId,
    PartnerId,
    TenantId,
    EnterpriseId,
  ]),
  from_account_id: z.union([
    LedgerAccountId,
    ContractorId,
    MerchantAccountId,
    BusinessId,
    PartnerId,
    TenantId,
    EnterpriseId,
  ]),
  amount: z.number().int(),
  source_type: TransferSourceType,
  source_id: z.union([AdjustmentId, PaymentId, RefundId]),
  description: noNulls(z.string().optional()),
  settlement_id: SettlementId,
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type Transfer = z.infer<typeof Transfer>;

export const Refund = z.object({
  id: RefundId,
  entity: z.literal(EntityType.Refund),
  status: RefundStatus,
  external: z.boolean(),
  amount: z.number().int(),
  account_id: MerchantAccountId,
  payment_id: PaymentId,

  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type Refund = z.infer<typeof Refund>;

export const PartnerSettings = z.object({
  id: PartnerSettingsId,
  entity: z.literal(EntityType.PartnerSettings),
  partner_id: PartnerId,
  enterprise_id: EnterpriseId,
  tenant_id: TenantId,

  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),

  contractor_cross_river_account_number: noNulls(z.string().optional()),
  legal_name: noNulls(z.string().optional()),
  bank_description: noNulls(z.string().optional()),
});
export type PartnerSettings = z.infer<typeof PartnerSettings>;

export const File = z.object({
  id: FileId,
  entity: z.literal(EntityType.File),
  purpose: FileBusinessPurpose,
  mimetype: FileSupportedMimeType,
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  deleted_at: noNulls(z.string().datetime({ offset: true }).optional()),
  filename: z.string(),
  size: z.number().int().gte(0),
  url: z.string(),
  owner_id: z.union([MerchantAccountId, ContractorId, BusinessId, PartnerId, TenantId, EnterpriseId]),
  owner_type: OwnerType,
  document_type: noNulls(FileDocumentType.optional()),
  source_id: z.union([MerchantApplicationId, DisputeId, BankAccountId]),
  source_type: noNulls(FileSourceType.optional()),
});
export type File = z.infer<typeof File>;

export const BankTransferData = z.object({
  id: z.string(),
  type: BankType,
  trace_number: z.string(),
  client_identifier: z.string(),
});
export type BankTransferData = z.infer<typeof BankTransferData>;

export const BankTransfer = z.object({
  id: BankTransferId,
  entity: z.literal(EntityType.BankTransfer),
  status: BankTransferStatus,
  transaction_count: z.number().int().gte(0),
  transaction_ids: z.array(TransactionId),
  amount: z.number().int(),
  transfer_data: noNulls(BankTransferData.optional()),
  transfer_reason: noNulls(noNulls(z.string().optional())),
  transfer_attempt_count: z.number().int().gte(0).default(0),
  failed_transfer_data: noNulls(z.array(BankTransferData).optional()),
  failure_message: noNulls(z.string().optional()),

  owner_type: LedgerAccountOwnerTypeEnum,
  owner_id: z.union([MerchantAccountId, ContractorId, BusinessId, PartnerId, TenantId, EnterpriseId]),
  tenant_id: TenantId,
  subledger_account_number: z.string(),
  subledger_account_type: BankType,

  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  completed_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type BankTransfer = z.infer<typeof BankTransfer>;

export const Note = z.object({
  id: NoteId,
  entity: z.literal(EntityType.Note),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  source_id: z.union([MerchantApplicationId, DisputeId, BankAccountId]),
  source_type: NoteSourceType,
  body: z.string(),
  originator_id: UserId,
  last_modifier_id: UserId,
});
export type Note = z.infer<typeof Note>;

export const ContractorSettings = z.object({
  id: ContractorSettingsId,
  entity: z.literal(EntityType.ContractorSettings),
  enterprise_id: EnterpriseId,
  tenant_id: TenantId,
  parent_id: z.union([MerchantAccountId, BusinessId, PartnerId, TenantId, EnterpriseId]),
  partner_id: PartnerId,
  contractor_id: ContractorId,
  parent_type: ContractorParentType,
  payment_processing_enabled: z.boolean().optional(),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
});
export type ContractorSettings = z.infer<typeof ContractorSettings>;

export const Reversal = z.object({
  id: ReversalId,
  entity: z.literal(EntityType.Reversal),
  amount: z.number().int(),
  account_id: MerchantAccountId,
  payment_id: PaymentId,
  reversal_reason: noNulls(z.string().optional()),
  reversal_code: noNulls(z.string().optional()),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type Reversal = z.infer<typeof Reversal>;

export const RefundFailure = z.object({
  id: RefundFailureId,
  entity: z.literal(EntityType.RefundFailure),
  refund_id: RefundId,
  account_id: MerchantAccountId,
  amount: z.number().int(),
  description: noNulls(z.string().optional()),

  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),

  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
});
export type RefundFailure = z.infer<typeof RefundFailure>;

export const NegativeListEntry = z.object({
  id: NegativeListEntryId,
  entity: z.literal(EntityType.NegativeList),
  createdAt: z.string().datetime({ offset: true }),
  updatedAt: z.string().datetime({ offset: true }),
  active: z.boolean(),
  metadata: noNulls(z.record(z.string(), z.string()).optional()),
  addedById: UserId,
  sourceId: z.string(),
  sourceType: z.string(),
  removedById: noNulls(z.string().optional()),
  removedAt: z.string().datetime({ offset: true }).optional(),
});
export type NegativeListEntry = z.infer<typeof NegativeListEntry>;

export const AchSettlementTransfer = z.object({
  id: AchSettlementTransferId,
  entity: z.literal(EntityType.AchSettlementTransfer),
  status: AchSettlementTransferStatus,
  owner_id: z.union([MerchantAccountId, ContractorId, BusinessId, PartnerId, TenantId, EnterpriseId]),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  transfer_id: BankTransferId.optional(),
  transfer_attempt_count: noNulls(z.number().int().optional()),
  total_amount: noNulls(z.number().int().optional()),
  total_count: noNulls(z.number().int().optional()),
  record_ids: z.array(z.string()).optional(),
  last_error_message: noNulls(z.string().optional()),
});
export type AchSettlementTransfer = z.infer<typeof AchSettlementTransfer>;

export const BillingDetails = z.object({
  address: z
    .object({
      city: noNulls(z.string().optional()),
      country: noNulls(z.string().optional()),
      state: noNulls(z.string().optional()),
      address_line1: noNulls(z.string().optional()),
      address_line2: noNulls(z.string().optional()),
      postal_code: noNulls(z.string().optional()),
    })
    .optional(),
  email: noNulls(z.string().optional()),
  name: noNulls(z.string().optional()),
  phone: noNulls(z.string().optional()),
});
export type BillingDetails = z.infer<typeof BillingDetails>;

export const BankGatewayAuthResponseSchema = z.object({
  id: z.string(),
  status: z.string(), // TODO: need to map these to proper status types once we fix the bug of saving them as GP types
  reference_id: z.string(),
  created_at: z.string(), // You might want to use z.date() if working with Date objects
  amount: z.coerce.number(),
  trace_number: z.string().optional(), // TODO all of these types seem wrong, but they are copied from the payment entity type
  sec_code: z.string().optional(),
  description: z.string().optional(),
  account_number: z.string().optional(),
  transaction_type: z.string().optional(), // This is push or pull, but the casing is inconsistent, TOOD: fix this at the processing level
  service_type: z.string().optional(),
});

export type BankGatewayAuthResponse = z.infer<typeof BankGatewayAuthResponseSchema>;

export const GatewayAuthResponseSchema = z.preprocess(
  (obj) => map(obj, 'address_line1check', 'address_line1_check'),
  z.object({
    amount: z.coerce.number(),
    status: GatewayResponseStatusType,
    response_raw: z.string().default(''),
    auth_code: noNulls(z.string().optional()),
    retrieval_reference: noNulls(z.string().optional()),
    invoice_number: noNulls(z.string().optional()),
    created_at: z.string(),
    reference: noNulls(z.string().optional()),
    response_text: noNulls(z.string().optional()),
    avs: noNulls(AVSCodeType.optional()),
    cvv: noNulls(CVVCodeType.optional()),
    entry_mode: noNulls(EntryMode.optional()),
    address_line1_check: noNulls(AVSCheckType.optional()),
    address_postal_code_check: noNulls(AVSCheckType.optional()),
    cvv_check: noNulls(CVVCheckType.optional()),
    token: noNulls(z.string().optional()),
    internal_gateway_reference_id: noNulls(z.string().optional()),
    signature: noNulls(PaymentSignature.optional()),
    emv_tag_data: z.string().optional(),
    receipt_data: z.any({}).optional(), // TODO: define receiptData structure
  }),
);

export type GatewayAuthResponse = z.infer<typeof GatewayAuthResponseSchema>;

const GatewayVoidResponseSchema = z.object({
  amount: z.coerce.number(),
  remaining_authorization_amount: z.coerce.number(),
  status: GatewayResponseStatusType,
  response_raw: z.string().default(''),
  created_at: z.string(),
  reference: noNulls(z.string().optional()),
  response_text: noNulls(z.string().optional()),
  internal_gateway_reference_id: noNulls(z.string().optional()),
});

export type GatewayVoidResponse = z.infer<typeof GatewayVoidResponseSchema>;

export const CardPaymentMethod = z.object({
  id: PaymentMethodId,
  entity: z.literal(EntityType.PaymentMethod),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  payment_method_type: z.literal(PaymentMethodType.enum.card),
  source_type: TokenSourceType,
  billing_details: noNulls(BillingDetails.optional()),
  meta_data: noNulls(z.record(z.string(), z.unknown()).optional()),
  single_use_token: noNulls(z.boolean().optional()),
  signature: noNulls(z.string().optional()),
  card: z.object({
    last_four_digits: z.string(),
    first_six_digits: noNulls(z.string().optional()),
    brand: CardBrand,
    exp_month: noNulls(z.string().optional()),
    exp_year: noNulls(z.string().optional()),
    wallet: CardWallet.optional(),
    from_terminal: noNulls(z.boolean().optional()),
    device_serial_number: noNulls(z.string().optional()),
    entry_mode: noNulls(EntryMode.optional()),
    reuse_eligible: noNulls(z.boolean().optional()),
    token: noNulls(z.string().optional()),
  }),
});
export type CardPaymentMethod = z.infer<typeof CardPaymentMethod>;

export const BankPaymentMethod = z.object({
  id: PaymentMethodId,
  entity: z.literal(EntityType.PaymentMethod),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  payment_method_type: z.literal(PaymentMethodType.enum.bank),
  source_type: TokenSourceType,
  meta_data: noNulls(z.record(z.string(), z.unknown()).optional()),
  billing_details: z.optional(BillingDetails),
  single_use_token: noNulls(z.boolean().optional()),
  signature: noNulls(z.string().optional()),
  bank: z.object({
    masked_account: noNulls(z.string().optional()),
    masked_routing: noNulls(z.string().optional()),
    owner_type: noNulls(BankAccountOwnerType.optional()),
    account_id: z.string(),
    subtype: z.string(),
    name: noNulls(z.string().optional()),
    official_name: noNulls(z.string().optional()),
    type: noNulls(z.string().optional()),
  }),
});
export type BankPaymentMethod = z.infer<typeof BankPaymentMethod>;

export const PaymentMethod = z.union([CardPaymentMethod, BankPaymentMethod]);
export type PaymentMethod = z.infer<typeof PaymentMethod>;

export const PaymentIntentOtherPayment = z.object({
  id: PaymentId,
  status: PaymentStatus,
  created_at: z.string(),
  updated_at: z.string(),
  response_text: noNulls(z.string().optional()),
  payment_method: PaymentMethod.optional(),
  gateway_void_responses: z.array(GatewayVoidResponseSchema).optional(),
  gateway_auth_response: GatewayAuthResponseSchema.nullable(),
  bank_gateway_auth_response: BankGatewayAuthResponseSchema.nullable(),
});

export type PaymentIntentOtherPayment = z.infer<typeof PaymentIntentOtherPayment>;

export const PaymentIntentFee = z.object({
  id: FeeId,
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  fee_type: FeeTypeEnum,
  fee_sub_type: noNulls(FeeSubTypeEnum.optional()),
  card_fee_bps: z.coerce.number().int().nullable(),
  auth_fee: z.coerce.number().int().nullable(),
  description: noNulls(z.string().optional()),
  source_type: FeeSourceEnum.optional(),
  source_id: PaymentIntentFeeSourceId.optional(),
  from_owner_id: z.string(),
  from_owner_type: LedgerAccountOwnerTypeEnum,
  settlement_id: SettlementId.nullable(),
  settlement_status: SettlementStatusEnum.nullable(),
  amount: z.coerce.number().int(),
});
export type PaymentIntentFee = z.infer<typeof PaymentIntentFee>;

export const PaymentIntentPayout = z.object({
  id: PayoutId,
  status: PayoutStatus,
  completed_at: z.string().datetime({ offset: true }).nullable(),
  created_at: z.string().datetime({ offset: true }),
});
export type PaymentIntentPayout = z.infer<typeof PaymentIntentPayout>;

export const PaymentIntentPlan = z.object({
  id: z.string(),
  effective_date: z.string().datetime({ offset: true }),
  plan_type: ProcessingPlanType,
});
export type PaymentIntentPlan = z.infer<typeof PaymentIntentPlan>;

export const PaymentIntentSplit = z.object({
  account_id: z.string(), //PartnerId,
  amount: z.number().int().gte(0),
  description: z.string(),
  name: z.string(),
});
export type PaymentIntentSplit = z.infer<typeof PaymentIntentSplit>;

export const PaymentIntentDetails = z.object({
  payment_intent_id: z.string(),
  fees: z.array(PaymentIntentFee).nullish(),
  payments: z.array(PaymentIntentOtherPayment).nullish(),
  payout: PaymentIntentPayout.nullish(),
  payment_splits: z.array(PaymentIntentSplit).nullish(),
  processing_plan: PaymentIntentPlan.nullish(),
});

export type PaymentIntentDetails = z.infer<typeof PaymentIntentDetails>;

export const Payment = z.object({
  id: PaymentId,
  entity: z.literal(EntityType.Payment),
  status: PaymentStatus,
  billing_details: noNulls(BillingDetails.optional()),
  amount: z.coerce.number().gte(0),
  amount_authorized: z.coerce.number().gte(0),
  amount_captured: z.coerce.number().gte(0),
  amount_refunded: z.coerce.number().gte(0),
  amount_voided: z.coerce.number().gte(0),
  amount_disputed: z.coerce.number().gte(0),
  amount_reversed: z.preprocess(
    (val) => (val === null || typeof val === 'undefined' ? 0 : val),
    z.coerce.number().gte(0),
  ),
  amount_rejected: z.preprocess(
    (val) => (val === null || typeof val === 'undefined' ? 0 : val),
    z.coerce.number().gte(0),
  ),
  cvv: noNulls(CVVCodeType.optional()),
  avs: noNulls(AVSCodeType.optional()),
  address_line1_check: noNulls(AVSCheckType.optional()),
  address_postal_code_check: noNulls(AVSCheckType.optional()),
  gateway_id: z.string(), // should this be union [Mock, Fiserv-Cardpointe, DBD-Cardpointe]?
  gateway_auth_response: noNulls(GatewayAuthResponseSchema.optional()),
  bank_gateway_auth_response: noNulls(BankGatewayAuthResponseSchema.optional()),
  cvv_check: noNulls(CVVCheckType.optional()),
  signature: noNulls(PaymentSignature.optional()),
  reversed: noNulls(z.boolean().optional()),
  reversal_id: noNulls(ReversalId.optional()),
  disputed: noNulls(z.boolean().optional()),
  dispute_id: noNulls(DisputeId.optional()),
  order_details: noNulls(PaymentOrderDetails.optional()),
  payment_intent_id: PaymentIntentId,
  payment_method_id: PaymentMethodId.optional(),
  payment_method: PaymentMethod.optional(),
  auth_code: noNulls(z.string().optional()),
  auth_response_text: noNulls(z.string().optional()),
  external: z.boolean(),
  card_present: noNulls(z.boolean().optional()),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  account_id: MerchantAccountId,
  reference_id: noNulls(z.string().optional()),
  user_fields: noNulls(z.record(z.string(), z.any()).optional()),
  description: noNulls(z.string().optional()),
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: noNulls(z.string().datetime({ offset: true }).optional()),
  settlement_id: noNulls(SettlementId.optional()),
});
export type Payment = z.infer<typeof Payment>;

export const PaymentIntent = z.object({
  id: PaymentIntentId,
  client_secret: noNulls(z.string().optional()),
  entity: z.literal(EntityType.PaymentIntent),
  status: PaymentIntentStatus,
  amount: z.coerce.number().gte(0),
  merchant_amount: noNulls(z.coerce.number().gte(0).optional()),
  amount_authorized: z.coerce.number().gte(0),
  amount_captured: z.coerce.number().gte(0),
  amount_refunded: z.coerce.number().gte(0),
  amount_voided: z.coerce.number().gte(0),
  amount_disputed: z.coerce.number().gte(0),
  amount_reversed: z.preprocess((val) => (!val ? 0 : val), z.coerce.number().gte(0)),
  amount_rejected: z.preprocess((val) => (!val ? 0 : val), z.coerce.number().gte(0)),
  application_fee_amount: noNulls(z.coerce.number().gte(0).optional()),
  payment_splits: z.array(PaymentSplit),
  order_details: noNulls(PaymentOrderDetails.optional()),
  capture: z.boolean(),
  currency: z.literal('USD'),
  latest_payment: noNulls(Payment.optional()),
  disputed: noNulls(z.boolean().optional()),
  dispute_id: noNulls(DisputeId.optional()),
  reversed: noNulls(z.boolean().optional()),
  reversal_id: noNulls(ReversalId.optional()),
  external: z.boolean(),
  partner_id: PartnerId,
  business_id: BusinessId,
  account_id: MerchantAccountId,
  payment_method_types: noNulls(z.array(PaymentMethodType).optional()),
  reference_id: noNulls(z.string().optional()),
  user_fields: noNulls(z.record(z.string(), z.any()).optional()),
  cancellation_reason: noNulls(z.string().optional()),
  description: noNulls(z.string().optional()),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  cancelled_at: z.string().datetime({ offset: true }).nullish(),
  captured_at: z.string().datetime({ offset: true }).nullish(),
  settlement_status: noNulls(SettlementStatusEnum.optional()),
  settled_at: z.string().datetime({ offset: true }).nullish(),
  settlement_id: noNulls(SettlementId.optional()),
  surcharge_amount: z.coerce.number().int().optional(),
  tax_amount: z.coerce.number().int().optional(),
  tip_amount: z.coerce.number().int().optional(),
});
export type PaymentIntent = z.infer<typeof PaymentIntent>;

export const CheckoutSession = z.object({
  id: CheckoutSessionId,
  entity: z.literal(EntityType.CheckoutSession),
  created_at: z.string().datetime({ offset: true }),
  updated_at: z.string().datetime({ offset: true }),
  cancel_url: z.string(),
  success_url: z.string(),
  customer_email: z.string(),
  reference_id: noNulls(z.string().optional()),
  user_fields: z.record(z.string(), z.any()).optional(),
  payment_method_types: z.array(PaymentMethodType).optional(),
  payment_intent_data: PaymentIntent.optional(),
  payout: Payout.optional(),
});
export type CheckoutSession = z.infer<typeof CheckoutSession>;

// TODO: Flesh out all the Zod types for this, See EventPayload type
export const ApplicationEvent = z.object({
  id: z.string(),
  event_type: EventType,
  payload: z.object({
    type: EventType,
    specversion: z.string(),
    source: z.string(), //EventSource,
    subject: z.string(),
    id: ApplicationEventId,
    datacontenttype: z.string(),
    time: z.string(),
    data: z.object({
      meta: z.object({
        auth: z
          .object({
            data: z.object({
              tenant_id: TenantId.nullish(),
              partner_id: PartnerId.nullish(),
              business_id: BusinessId.nullish(),
              merchant_id: MerchantAccountId.nullish(),
              self_type: AuthUserSelfType.nullish(),
              self_id: z.string().nullish(),
              self_subtype: AuthUserSelfSubType.nullish(),
            }),
            context: z.object({
              account_id: MerchantAccountId.nullish(),
              business_id: BusinessId.nullish(),
              enterprise_id: EnterpriseId.nullish(),
              tenant_id: TenantId.nullish(),
              partner_id: PartnerId.nullish(),
            }),
          })
          .optional(),
      }),
      object: z.any(),
    }),
  }),
  created_at: dtString,
  event_created_at: dtString.optional(),
  username: noNulls(z.string().optional()),
});

export type ApplicationEvent = z.infer<typeof ApplicationEvent>;
