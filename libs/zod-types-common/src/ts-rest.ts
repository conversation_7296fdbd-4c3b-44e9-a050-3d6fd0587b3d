import { type ZodSchema } from 'zod';

import { DefaultResponses } from './errors.js';

export const postSchema = <
  Params extends ZodSchema,
  Body extends ZodSchema,
  Response extends ZodSchema,
  Headers extends ZodSchema,
>({
  path,
  body,
  res,
  pathParams,
  headers,
  summary,
  description,
  metadata,
}: {
  path: string;
  body: Body;
  res: Response;
  pathParams?: Params;
  headers?: Headers;
  summary?: string;
  description?: string;
  metadata?: Record<string, unknown>;
}) => {
  return {
    method: 'POST' as const,
    path,
    ...(pathParams ? { pathParams } : {}),
    body,
    ...(headers ? { headers } : {}),
    ...(summary ? { summary } : {}),
    ...(description ? { description } : {}),
    ...(metadata ? { metadata } : {}),
    responses: {
      200: res,
      ...DefaultResponses,
    },
    hide: process.env.HIDE_ROUTES !== undefined,
  };
};
export const getSchema = <
  Params extends ZodSchema,
  Query extends ZodSchema,
  Response extends ZodSchema,
  Head<PERSON> extends ZodSchema,
>({
  path,
  pathParams,
  query,
  res,
  headers,
  summary,
  description,
  metadata,
}: {
  path: string;
  pathParams?: Params;
  summary?: string;
  description?: string;
  query?: Query;
  res: Response;
  headers?: Headers;
  metadata?: Record<string, unknown>;
}) => {
  return {
    method: 'GET' as const,
    path,
    ...(pathParams ? { pathParams } : {}),
    ...(query ? { query } : {}),
    ...(headers ? { headers } : {}),
    ...(summary ? { summary } : {}),
    ...(description ? { description } : {}),
    ...(metadata ? { metadata } : {}),
    responses: {
      200: res,
      ...DefaultResponses,
    },
    hide: process.env.HIDE_ROUTES !== undefined,
  };
};

export const patchSchema = <
  Params extends ZodSchema,
  Body extends ZodSchema,
  Response extends ZodSchema,
  Headers extends ZodSchema,
>({
  path,
  body,
  res,
  pathParams,
  headers,
  summary,
  description,
  metadata,
}: {
  path: string;
  body: Body;
  res: Response;
  pathParams?: Params;
  headers?: Headers;
  summary?: string;
  description?: string;
  metadata?: Record<string, unknown>;
}) => {
  return {
    method: 'PATCH' as const,
    path,
    ...(pathParams ? { pathParams } : {}),
    body,
    ...(headers ? { headers } : {}),
    ...(summary ? { summary } : {}),
    ...(description ? { description } : {}),
    ...(metadata ? { metadata } : {}),
    responses: {
      200: res,
      ...DefaultResponses,
    },
    hide: process.env.HIDE_ROUTES !== undefined,
  };
};
