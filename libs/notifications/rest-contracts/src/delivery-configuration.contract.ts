import { getSchema, patchSchema, postSchema } from '@dbd/zod-types-common';
import {
  CreateDeliveryConfigurationSchema,
  DeliveryConfigurationSchema,
  UpdateDeliveryConfigurationSchema,
} from '@notifications/types';
import { initContract } from '@ts-rest/core';

const c = initContract();

export const deliveryConfigurationContract = c.router({
  createDeliveryConfiguration: postSchema({
    path: '/delivery_configurations',
    res: DeliveryConfigurationSchema,
    body: CreateDeliveryConfigurationSchema,
    summary: 'Create a delivery configuration',
    description: 'Create a delivery configuration',
    metadata: {
      openApiTags: ['delivery-configurations'],
    },
  }),
  updateDeliveryConfiguration: patchSchema({
    path: '/delivery_configurations',
    res: DeliveryConfigurationSchema,
    body: UpdateDeliveryConfigurationSchema,
    summary: 'Update a delivery configuration',
    description: 'Update a delivery configuration',
    metadata: {
      openApiTags: ['delivery-configurations'],
    },
  }),
  getByOwnerId: getSchema({
    path: '/delivery_configurations',
    res: DeliveryConfigurationSchema,
    summary: 'Fetch a delivery configuration by owner id',
    metadata: {
      openApiTags: ['delivery-configurations'],
    },
  }),
});
