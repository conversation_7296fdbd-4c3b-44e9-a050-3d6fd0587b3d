{"extends": "../../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": false, "forceConsistentCasingInFileNames": true, "types": ["node"]}, "include": ["src/**/*.ts"], "references": [{"path": "../email-service/tsconfig.lib.json"}, {"path": "../types/tsconfig.lib.json"}, {"path": "../../utils/tsconfig.lib.json"}, {"path": "../../core-types/tsconfig.lib.json"}, {"path": "../../service-client-library/tsconfig.lib.json"}], "exclude": ["vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx"]}