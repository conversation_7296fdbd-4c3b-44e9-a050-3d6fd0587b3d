import { Entity, FormattedItem } from 'dynamodb-toolbox/entity';
import { boolean } from 'dynamodb-toolbox/schema/boolean';
import { item } from 'dynamodb-toolbox/schema/item';
import { map } from 'dynamodb-toolbox/schema/map';
import { string } from 'dynamodb-toolbox/schema/string';
import { Table } from 'dynamodb-toolbox/table';

/**
 * User Notification Preference Entity attributes for DynamoDB Toolbox
 * Mirrors the UserNotificationPreference schema in types/src/user-notification-preference.schema.ts
 */
const attributes = {
  // Attributes
  id: string().key(),
  tenantId: string().savedAs('tenant_id').key(),
  userId: string().savedAs('user_id').key(),
  category: string().enum('onboarding', 'transactional').savedAs('category'),
  channels: map({
    email: boolean(),
  }).savedAs('channels'),
  frequency: string().enum('immediate', 'daily_digest', 'weekly_digest').savedAs('frequency'),
  quietHours: map({
    enabled: boolean(),
    startTime: string().savedAs('start_time').optional(),
    endTime: string().savedAs('end_time').optional(),
  }).savedAs('quiet_hours'),
};

export const UserNotificationPreferenceSchema = item(attributes);

/**
 * Returns a User Notification Preference Entity for the given table
 * @param table - DynamoDB Toolbox Table instance
 * @returns Entity instance for user notification preference
 */
export const getUserNotificationPreferenceEntity = (
  table: Table<
    { name: 'PK'; type: 'string' },
    { name: 'SK'; type: 'string' },
    {
      GSI1: {
        type: 'global';
        partitionKey: { name: 'GSI1PK'; type: 'string' };
        sortKey: { name: 'GSI1SK'; type: 'string' };
      };
    },
    'type'
  >,
) => {
  return new Entity({
    name: 'user_notification_preference',
    table,
    schema: UserNotificationPreferenceSchema,
    computeKey: (data) => ({
      // Reason: Partition by tenant and user for efficient queries
      PK: `TENANT#${(data as any).tenantId}#USER#${(data as any).userId}`,
      SK: `USER_NOTIFICATION_PREFERENCE#${(data as { id: string }).id}`,
    }),
    timestamps: {
      created: {
        name: 'createdAt',
        savedAs: 'created_at',
      },
      modified: {
        name: 'updatedAt',
        savedAs: 'updated_at',
      },
    },
  });
};

export type UserNotificationPreferenceEntity = ReturnType<typeof getUserNotificationPreferenceEntity>;

export type FormattedUserNotificationPreferenceEntity = FormattedItem<UserNotificationPreferenceEntity>;
