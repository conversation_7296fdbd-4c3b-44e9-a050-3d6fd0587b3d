import { z } from 'zod';

export const TimestampSchema = z.string().datetime();
export const JsonSchema = z.record(z.string(), z.any());

/**
 * Schema for notification template types
 * Defines the specific types of notifications that can be sent
 */
export const NotificationType = {
  DOCUMENT_REQUEST: 'document_request',
  TRANSACTION_HELD: 'transaction_held',
  PAYOUTS_DISABLED: 'payouts_disabled',
} as const;

export type NotificationType = (typeof NotificationType)[keyof typeof NotificationType];

export const NotificationTypeSchema = z.nativeEnum(NotificationType);

export type NotificationTypeSchema = z.infer<typeof NotificationTypeSchema>;

export const NotificationDeliveryType = {
  EMAIL: 'email',
  //   SMS: 'sms',
  //   PUSH: 'push',
  //   IN_APP: 'in_app',
} as const;

export type NotificationDeliveryType = (typeof NotificationDeliveryType)[keyof typeof NotificationDeliveryType];

export const NotificationCategorySchema = z.enum(['onboarding', 'transactional']);

/**
 * Shared error response schema for all API endpoints
 */
export const ErrorResponseSchema = z.object({
  message: z.string(),
  code: z.string().optional(),
  details: z.record(z.string(), z.unknown()).optional(),
});

export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
