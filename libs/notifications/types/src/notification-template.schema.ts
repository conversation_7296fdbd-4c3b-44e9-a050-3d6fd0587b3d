import 'zod-openapi/extend';

import { z } from 'zod';

import { NotificationTypeSchema, TimestampSchema } from './common.js';

const EmailTemplateSchema = z.object({
  subject: z.string().max(250),
  htmlTemplate: z.string().max(4000),
  plainTextTemplate: z.string().max(4000).optional(),
  externalTemplateId: z.string().optional().openapi({
    description: 'external template ID, e.g. SendGrid template ID',
  }),
});

// TODO: Add SMS template schema
// const SmsTemplateSchema = z.object({
//   template: z.string(),
// });

const TemplateChannelsSchema = z.object({
  email: EmailTemplateSchema.optional(),
  //   sms: SmsTemplateSchema.optional(),
});

// NotificationTemplate Schema
/*
{
  "id": "f9e8d7c6-b5a4-3f2e-1d0c-9b8a7d6e5f4e",
  "name": "welcome_email",
  "category": "onboarding",
  "title": "Welcome to {{partner.name}}"
  "content": "Thank you for joining {{partner.name}}! We're excited to have you on board.",
  "channels": {
    "email": {
      "subject": "Welcome to {{partner.name}}"
      "htmlTemplate": "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'><div style='background-color: {{partner.brandingColors.primary}}; height: 5px;'></div><img src='{{partner.logo}}' alt='{{partner.name}}' style='max-width: 200px; margin: 20px 0;' /><h1>Welcome to {{partner.name}}!</h1><p>Hello {{userName}},</p><p>{{content}}</p><div style='margin: 30px 0;'><h2>Getting Started</h2><ul><li>Complete your profile</li><li>Explore our products</li><li>Check out our mobile app</li></ul></div><div style='text-align: center;'><a href='{{partner.url}}' style='background-color: {{partner.brandingColors.primary}}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Get Started</a></div><hr style='border: none; border-top: 1px solid #eee; margin: 20px 0;' /><p style='color: #666; font-size: 12px;'>If you have any questions, please contact {{partner.supportEmail}}</p></div>",
      "plainTextTemplate": "Welcome to {{partner.name}}!\n\nHello {{userName}},\n\n{{content}}\n\nGetting Started:\n- Complete your profile\n- Explore our products\n- Check out our mobile app\n\nGet started: {{partner.url}}\n\nIf you have any questions, please contact {{partner.supportEmail}}"
    },
    "push": {
      "title": "Welcome to {{partner.name}}"
      "body": "Thanks for joining! Tap to get started."
      "actionUrl": "/welcome",
      "image": "{{partner.logo}}"
    },
    "inApp": {
      "template": "<div class='notification welcome'><i class='icon-welcome'></i> <strong>Welcome to {{partner.name}}!</strong><p>Tap here to get started</p></div>",
      "icon": "star",
      "actionUrl": "/welcome"
    }
  },
  "priority": "normal",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}

*/

/**
 * Schema for notification template
 * Defines the specific categories of notifications that can be sent
 */
export const BaseNotificationTemplateSchema = z.object({
  id: z.string(),
  notificationType: NotificationTypeSchema,
  title: z.string().max(200),
  content: z.string().max(1000),
  channels: TemplateChannelsSchema,
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema,
});

export type BaseNotificationTemplateSchema = z.infer<typeof BaseNotificationTemplateSchema>;

export const TenantNotificationTemplateSchema = BaseNotificationTemplateSchema.extend({
  templateType: z.literal('tenant'),
});

export type TenantNotificationTemplateSchema = z.infer<typeof TenantNotificationTemplateSchema>;

export const PartnerNotificationTemplateSchema = BaseNotificationTemplateSchema.extend({
  templateType: z.literal('partner'),
});

export type PartnerNotificationTemplateSchema = z.infer<typeof PartnerNotificationTemplateSchema>;

// ****************************

export const NotificationTemplateSchema = z.discriminatedUnion('templateType', [
  TenantNotificationTemplateSchema,
  PartnerNotificationTemplateSchema,
]);

export type NotificationTemplateSchema = z.infer<typeof NotificationTemplateSchema>;

const BaseCreateNotificationTemplateSchema = BaseNotificationTemplateSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Create notification template request schema and type
 */
export const CreateNotificationTemplateSchema = BaseCreateNotificationTemplateSchema;

export type CreateNotificationTemplateSchema = z.infer<typeof CreateNotificationTemplateSchema>;

export const BaseUpdateNotificationTemplateSchema = BaseNotificationTemplateSchema.partial().omit({
  id: true,
  notificationType: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Update notification template request schema and type
 */
export const UpdateNotificationTemplateSchema = BaseUpdateNotificationTemplateSchema;

export type UpdateNotificationTemplateSchema = z.infer<typeof UpdateNotificationTemplateSchema>;

/**
 * List notification templates request schema and type
 */
export const ListNotificationTemplatesSchema = z.object({
  data: z.array(NotificationTemplateSchema),
});

export type ListNotificationTemplatesSchema = z.infer<typeof ListNotificationTemplatesSchema>;

// Example of a notification template

export const NotificationTemplateExample: NotificationTemplateSchema = {
  id: 'f9e8d7c6-b5a4-3f2e-1d0c-9b8a7d6e5f4e',
  templateType: 'tenant',
  notificationType: 'document_request',
  title: 'Welcome to {{partner.name}}',
  content: "Thank you for joining {{partner.name}}! We're excited to have you on board.",
  channels: {
    email: {
      subject: 'Welcome to {{partner.name}}',
      htmlTemplate: 'Welcome to {{partner.name}}!',
      plainTextTemplate: 'Welcome to {{partner.name}}!',
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};
