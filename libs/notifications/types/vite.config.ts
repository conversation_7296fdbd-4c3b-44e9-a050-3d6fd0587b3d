import { defineConfig } from 'vite';

export default defineConfig(() => ({
  root: __dirname,
  cacheDir: '../../../node_modules/.vite/libs/notifications/types',
  plugins: [],
  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },
  test: {
    watch: false,
    globals: true,
    environment: 'node',
    passWithNoTests: true,
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      provider: 'v8' as const,
      enabled: true,
      reportsDirectory: '../../../coverage/libs/errors',
      include: ['src/**/*.ts'],
      exclude: ['vite.config.ts', 'src/**/*.spec.ts'],
      reporter: ['clover', 'json', 'lcov', 'text'],
    },
  },
}));
