{"extends": "../../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": false, "forceConsistentCasingInFileNames": true, "types": ["node"]}, "include": ["src/**/*.ts"], "references": [{"path": "../types/tsconfig.lib.json"}, {"path": "../../zod-types-common/tsconfig.lib.json"}, {"path": "../../zod-types/tsconfig.lib.json"}, {"path": "../../fastify/tsconfig.lib.json"}, {"path": "../../errors/tsconfig.lib.json"}, {"path": "../lib/tsconfig.lib.json"}, {"path": "../rest-contracts/tsconfig.lib.json"}], "exclude": ["vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx"]}