import '@fastify/sensible';

import { BadRequestError, NotFoundError, ServerError } from '@dbd/zod-types-common';
import { NotificationTemplateService } from '@notifications/lib';
import { templatesContract } from '@notifications/rest-contracts';
import { initServer } from '@ts-rest/fastify';

import { AuthenticatedRequest } from './types.js';

const tsRestServer = initServer();

export class NotificationTemplatesController {
  private notificationTemplateService: NotificationTemplateService;

  constructor(notificationTemplateService: NotificationTemplateService) {
    this.notificationTemplateService = notificationTemplateService;
  }

  /**
   * Get a notification template by notificationType and ownerId
   */
  getNotificationTemplate = tsRestServer.route(
    templatesContract.getNotificationTemplate,
    async ({ params: { notificationType }, request }) => {
      const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
      const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
      const ownerId = partnerId || tenantId;
      if (!ownerId) {
        return BadRequestError.fromError(new Error('Partner ID or Tenant ID is required')).toResponse();
      }

      const template = await this.notificationTemplateService.getTemplate(notificationType, ownerId);

      if (!template) {
        return {
          status: 404,
          body: {
            message: 'Template not found',
            code: 'TEMPLATE_NOT_FOUND',
          },
        };
      }
      const resp = this.mapTemplateToResponse(template);
      if (!resp) {
        return {
          status: 404,
          body: {
            message: 'Template not found',
            code: 'TEMPLATE_NOT_FOUND',
          },
        };
      } else {
        return {
          status: 200,
          body: resp,
        };
      }
    },
  );

  /**
   * Create a new notification template
   */
  createNotificationTemplate = tsRestServer.route(
    templatesContract.createNotificationTemplate,
    async ({ body, request }) => {
      const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
      const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
      const owner = {
        tenantId,
        partnerId,
      };
      const template = await this.notificationTemplateService.createTemplate(
        {
          ...body,
        },
        owner,
      );
      const resp = this.mapTemplateToResponse(template);
      if (!resp) {
        return {
          status: 500,
          body: {
            message: 'Failed to format template response',
            code: 'TEMPLATE_FORMAT_ERROR',
          },
        };
      }

      return {
        status: 200,
        body: resp,
      };
    },
  );

  /**
   * Update a notification template
   */
  updateNotificationTemplate = tsRestServer.route(
    templatesContract.updateNotificationTemplate,
    async ({ params: { notificationType }, body, request }) => {
      const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
      const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
      const ownerId = partnerId || tenantId;
      if (!ownerId) {
        return BadRequestError.fromError(new Error('Partner ID or Tenant ID is required')).toResponse();
      }
      try {
        const updatedTemplate = await this.notificationTemplateService.updateTemplate({
          notificationType,
          ownerId,
          updates: body,
        });

        if (!updatedTemplate) {
          return NotFoundError.fromError(
            new Error(`Template not found: ${notificationType} for ${ownerId}`),
          ).toResponse();
        }

        const resp = this.mapTemplateToResponse(updatedTemplate);
        if (!resp) {
          return ServerError.fromError(new Error('Failed to format template response')).toResponse();
          return {
            status: 500,
            body: {
              message: 'Failed to format template response',
              code: 'TEMPLATE_FORMAT_ERROR',
            },
          };
        }

        return {
          status: 200,
          body: resp,
        };
      } catch (error) {
        return {
          status: 500,
          body: {
            message: error instanceof Error ? error.message : 'Failed to update template',
            code: 'TEMPLATE_UPDATE_ERROR',
          },
        };
      }
    },
  );

  /**
   * List all notification templates for an owner
   */
  listNotificationTemplates = tsRestServer.route(templatesContract.listNotificationTemplates, async ({ request }) => {
    const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
    const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
    const ownerId = partnerId || tenantId;
    if (!ownerId) {
      throw request.server.httpErrors.unauthorized();
    }
    const templates = await this.notificationTemplateService.listTemplates(ownerId);

    const resp = templates
      .map((template) => this.mapTemplateToResponse(template))
      .filter((template): template is NonNullable<typeof template> => template !== undefined);

    return {
      status: 200,
      body: {
        data: resp,
      },
    };
  });

  private mapTemplateToResponse(
    template: Awaited<ReturnType<typeof this.notificationTemplateService.getTemplateById>>,
  ) {
    if (!template) {
      return;
    }

    if (template.templateType === 'tenant') {
      // Only return tenant fields
      const { templateType, id, notificationType, title, content, channels, createdAt, updatedAt } = template;

      return {
        templateType,
        id,
        notificationType,
        title,
        content,
        channels,
        createdAt,
        updatedAt,
      };
    } else if (template.templateType === 'partner') {
      // Only return partner fields
      const { templateType, id, notificationType, title, content, channels, createdAt, updatedAt } = template;
      return {
        templateType,
        id,
        notificationType,
        title,
        content,
        channels,
        createdAt,
        updatedAt,
      };
    }
    return;
  }
}
