import { auth } from '@dbd/next-sessions/auth';
import { MerchantApplicationRow } from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import {
  MerchantApplicationForm,
  MerchantApplicationFormContent,
} from '@dbd/ui-merchant-apps/blocks/merchant-application-form';
import { UpdateMerchantAppSubmitHandler } from '@dbd/ui-merchant-apps/blocks/merchant-application-form.actions';
import { getMerchantApplicationById } from '@dbd/ui-merchant-apps/lib/merchant-application.loaders';
import {
  getPlanSchemaFromApplicationRow,
  MerchantApplicationFormSchema,
} from '@dbd/ui-merchant-apps/schemas/merchant-application-form-schema';
import type { MerchantAccountOwnerTitle } from '@dbd/zod-types/enums';
import { type BusinessId, type MerchantApplicationId, type PartnerId } from '@dbd/zod-types-common';
import { formatISO } from 'date-fns';
import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';

import PortalLayout from '@/components/portal-layout';
import { generatePageMetadata } from '@/lib/generateMetadata';
import { GlobalSearchParamsCache, serializeGlobalParams } from '@/lib/global-search-params';

import { ApplicationEditTopbar } from './application-edit-topbar';

type PageProps = {
  searchParams: Promise<any>;

  params: Promise<{
    id: MerchantApplicationId;
  }>;
};

export default async function ApplicationEditPage({ searchParams, params }: PageProps) {
  const { user } = await auth();
  if (!user) {
    return redirect('/auth/login');
  }
  const globalState = GlobalSearchParamsCache.parse(await searchParams);
  const { id } = await params;
  const application = await getMerchantApplicationById({
    params: {
      id,
      fields: [],
    },
  });

  if (!application || !application.partner_processing_plan_id) {
    return notFound();
  }

  if (application.tenant_id !== user.tenant_id || (user.partner_id && application.partner_id !== user.partner_id)) {
    return notFound();
  }

  if (application.status !== 'DRAFT') {
    return redirect(serializeGlobalParams(`/applications/${application.id}`, globalState));
  }

  const onSuccess = async (data: MerchantApplicationRow) => {
    'use server';

    redirect(serializeGlobalParams(`/applications/${data.id}`, globalState));
  };

  const mergedPlan = getPlanSchemaFromApplicationRow(application);
  const defaultValues: MerchantApplicationFormSchema = {
    id: application.id,
    name: application.account_name ?? '',
    businessId: application.business_id as BusinessId,
    partnerId: application.partner_id as PartnerId,
    plan_id: application?.partner_processing_plan_id ?? undefined,
    plan: mergedPlan,
    owners:
      application.owners?.map((owner) => ({
        first_name: owner.first_name ?? '',
        last_name: owner.last_name ?? '',
        ssn: {
          mask: owner.ssn?.mask ? 'XXX-XX-' + owner.ssn.mask : '',
          value: '',
        },
        email: owner.email ?? '',
        title: owner.title ?? ('' as MerchantAccountOwnerTitle),
        ownership_percent: owner.ownership_percent ?? 0,
        birth_date: owner.birth_date ?? null,
        phone_number: owner.phone_number ?? '',
        residence_address: {
          country: (owner.residence_address?.country ?? '') as 'US',
          state: (owner.residence_address?.state ?? '') as 'AK',
          city: owner.residence_address?.city ?? '',
          zip_code: owner.residence_address?.zip_code ?? '',
          address_line1: owner.residence_address?.address_line1 ?? '',
          address_line2: owner.residence_address?.address_line2 ?? '',
        },
      })) ?? [],
    address: {
      address_line1: application.address_line_1 ?? '',
      address_line2: application.address_line_2 ?? '',
      city: application.address_city ?? '',
      state: (application.address_state ?? '') as 'AK',
      zip_code: application.address_zip_code ?? '',
      country: (application.address_country ?? '') as 'US',
    },
    company: {
      legal_name: application.company_legal_name ?? '',
      description: application.company_desc ?? '',
      dba_name: application.company_dba_name ?? '',
      tax_id: {
        mask:
          application.company_ein_mask && application.company_ownership_type === 'SOLO_TRADER'
            ? 'XX-XXX-' + application.company_ein_mask
            : application.company_ein_mask
              ? 'XX-XXXXX' + application.company_ein_mask
              : '',
        value: '',
      },
      phone_number: application.company_phone ?? '',
      website: application.company_website ?? '',
      business_start_date: (application.company_start_date && formatISO(application.company_start_date)) || '',
      ownership_type: application.company_ownership_type ?? '',
      category: application.company_category ?? '',
      mcc: application.company_mcc ?? '',
      stock_symbol: application.company_stock_symbol ?? '',
    },
    industry_volume: {
      avg_annual_volume: application.avg_annual_volume ?? 0,
      avg_ach_annual_volume: application.avg_ach_annual_volume ?? 0,
      avg_ticket_amount: application.avg_ticket_amount ?? 0,
      high_ticket_amount: application.high_ticket_amount ?? 0,
      avg_ach_ticket_amount: application.avg_ach_ticket_amount ?? 0,
      ach_high_ticket_amount: application.ach_high_ticket_amount ?? 0,
    },
    transaction_modes: {
      in_person: application.transaction_modes?.in_person ?? 0,
      online: application.transaction_modes?.online ?? 0,
    },
    service_deliveries: {
      same_day_delivery: application.services_deliveries?.same_day_delivery ?? 0,
      one_week_delivery: application.services_deliveries?.one_week_delivery ?? 0,
      two_week_delivery: application.services_deliveries?.two_week_delivery ?? 0,
      one_month_delivery: application.services_deliveries?.one_month_delivery ?? 0,
      more_than_one_month_delivery: application.services_deliveries?.more_than_one_month_delivery ?? 0,
    },
    sales_code_id: application.sales_code_id ?? '',
    sales_agent_id: application.sales_agent_user_id ?? '',
  };

  return (
    <MerchantApplicationForm
      defaultValues={defaultValues}
      onSubmit={UpdateMerchantAppSubmitHandler}
      user={user}
      onSuccess={onSuccess}
    >
      <PortalLayout topbar={<ApplicationEditTopbar application={application} />}>
        <MerchantApplicationFormContent />
      </PortalLayout>
    </MerchantApplicationForm>
  );
}

export async function generateMetadata({ params }: PageProps) {
  try {
    const { id } = await params;
    const account = await getMerchantApplicationById({
      params: {
        id,
        fields: [],
      },
    }).then((application) => application?.account_name);

    if (!account?.trim()) {
      return null as unknown as Metadata;
    }

    return await generatePageMetadata({
      type: 'Editing Application',
      canonical: `/applications/${id}`,
      identifier: account,
    });
  } catch (_e) {
    return null as unknown as Metadata;
  }
}
